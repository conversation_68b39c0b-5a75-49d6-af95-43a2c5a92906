#!/usr/bin/env python3
"""
Test different Cortex models and find the best one
"""

import os
import sys
import json
import getpass
from snowflake.snowpark import Session
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_snowflake_session():
    """Get Snowflake session with credentials"""
    
    # Load config
    config_path = "config/snowflake_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {
            "account": "GNBXJLF-PKB97538",
            "user": "ASHUTOSH1",
            "warehouse": "SALESGENIE_AI_WH",
            "database": "SALESGENIE_AI",
            "schema": "CRM_DATA",
            "role": "ACCOUNTADMIN"
        }
    
    # Get password securely
    if config.get("password") == "XXXXXXXXXXXXXXXXXX" or not config.get("password"):
        password = getpass.getpass(f"Enter password for {config['user']}: ")
    else:
        password = config["password"]
    
    config["password"] = password
    
    try:
        logger.info("Creating Snowpark session...")
        session = Session.builder.configs(config).create()
        logger.info("✅ Snowpark session created successfully!")
        return session
    except Exception as e:
        logger.error(f"Failed to create Snowpark session: {str(e)}")
        return None

def test_cortex_models(session):
    """Test different Cortex models"""
    
    # Latest Cortex models as of 2024
    models_to_test = [
        'llama3.1-405b',      # Latest and most capable
        'llama3.1-70b',       # Good balance of capability and speed
        'llama3.1-8b',        # Fastest
        'snowflake-arctic',   # Snowflake's own model
        'mixtral-8x7b',       # Good alternative
        'llama3-70b',         # Previous generation
        'llama3-8b',          # Previous generation
        'mistral-large',      # Mistral's large model
        'reka-flash',         # Fast model
        'gemma-7b'            # Google's model
    ]
    
    working_models = []
    
    for model in models_to_test:
        try:
            logger.info(f"Testing model: {model}")
            
            test_query = f"""
            SELECT SNOWFLAKE.CORTEX.COMPLETE(
                '{model}',
                'Respond with exactly: SUCCESS - {model} is working'
            ) as response
            """
            
            result = session.sql(test_query).collect()
            
            if result and len(result) > 0:
                response = result[0]['RESPONSE']
                logger.info(f"✅ {model}: {response[:100]}...")
                working_models.append(model)
            else:
                logger.warning(f"❌ {model}: No response")
                
        except Exception as e:
            logger.warning(f"❌ {model}: {str(e)}")
    
    return working_models

def test_with_real_data(session, model):
    """Test model with real CRM data"""
    try:
        logger.info(f"Testing {model} with real CRM data...")
        
        # Get actual data from your database
        data_query = """
        SELECT 
            COUNT(*) as total_opportunities,
            SUM(amount) as total_value,
            AVG(probability) as avg_probability
        FROM salesgenie_ai.crm_data.opportunities
        WHERE is_won = FALSE AND is_lost = FALSE
        """
        
        data_result = session.sql(data_query).collect()
        if data_result:
            total_opps = data_result[0][0]
            total_value = data_result[0][1]
            avg_prob = data_result[0][2]
            
            prompt = f"""Analyze this real sales pipeline data:
            - Total active opportunities: {total_opps}
            - Total pipeline value: ${total_value:,.2f}
            - Average probability: {avg_prob:.1f}%
            
            Provide 3 key insights and recommendations for this sales pipeline."""
            
            cortex_query = f"""
            SELECT SNOWFLAKE.CORTEX.COMPLETE(
                '{model}',
                '{prompt.replace("'", "''")}'
            ) as response
            """
            
            result = session.sql(cortex_query).collect()
            
            if result and len(result) > 0:
                response = result[0]['RESPONSE']
                logger.info(f"✅ {model} analysis successful!")
                logger.info(f"Response preview: {response[:200]}...")
                return True, response
            else:
                logger.error(f"❌ {model} returned no response")
                return False, None
                
    except Exception as e:
        logger.error(f"❌ Error testing {model} with real data: {str(e)}")
        return False, None

def main():
    """Main function"""
    logger.info("🧪 Testing Cortex models...")
    
    # Get Snowflake session
    session = get_snowflake_session()
    if not session:
        logger.error("❌ Could not create Snowflake session")
        return False
    
    try:
        # Test all models
        working_models = test_cortex_models(session)
        
        if working_models:
            logger.info(f"✅ Working models: {working_models}")
            
            # Test the best model with real data
            best_model = working_models[0]  # First one is usually the best
            logger.info(f"🎯 Testing best model '{best_model}' with real data...")
            
            success, response = test_with_real_data(session, best_model)
            
            if success:
                print("\n" + "="*60)
                print(f"🎉 BEST MODEL FOUND: {best_model}")
                print("="*60)
                print(f"\n📊 Real Data Analysis:")
                print(response)
                print(f"\n✅ Update your Streamlit app to use: '{best_model}'")
                return True
            else:
                logger.error(f"❌ {best_model} failed with real data")
                return False
        else:
            logger.error("❌ No working Cortex models found")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error during testing: {str(e)}")
        return False
    
    finally:
        if session:
            session.close()

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
