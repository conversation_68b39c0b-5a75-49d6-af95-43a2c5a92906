/*
SalesGenie AI - Database Setup Script
Nihilent x Snowflake Hackathon 2025

This script creates the foundational database structure for the SalesGenie AI platform
*/

-- =====================================================
-- ROLE AND PRIVILEGE SETUP
-- =====================================================

-- Use ACCOUNTADMIN role for setup
USE ROLE ACCOUNTADMIN;

-- =====================================================
-- DATABASE AND SCHEMA CREATION
-- =====================================================

-- Create main database
CREATE OR REPLACE DATABASE salesgenie_ai
COMMENT = 'SalesGenie AI - Conversational AI for Sales Excellence';

-- Create schemas for different data domains
CREATE OR REPLACE SCHEMA salesgenie_ai.crm_data
COMMENT = 'Core CRM data including leads, opportunities, customers';

CREATE OR REPLACE SCHEMA salesgenie_ai.analytics
COMMENT = 'Analytics tables, views, and ML models';

CREATE OR REPLACE SCHEMA salesgenie_ai.ai_services
COMMENT = 'AI service configurations and semantic models';

CREATE OR REPLACE SCHEMA salesgenie_ai.integration
COMMENT = 'External system integration tables and staging';

-- =====================================================
-- WAREHOUSE CREATION
-- =====================================================

CREATE OR REPLACE WAREHOUSE salesgenie_ai_wh
    WAREHOUSE_SIZE = 'LARGE'
    WAREHOUSE_TYPE = 'STANDARD'
    AUTO_SUSPEND = 60
    AUTO_RESUME = TRUE
    INITIALLY_SUSPENDED = TRUE
    COMMENT = 'Warehouse for SalesGenie AI operations';

-- Use the warehouse and database
USE ROLE ACCOUNTADMIN;
USE WAREHOUSE salesgenie_ai_wh;
USE DATABASE salesgenie_ai;

-- =====================================================
-- STAGE CREATION FOR DATA LOADING
-- =====================================================

USE SCHEMA salesgenie_ai.integration;

-- Create stages for different data sources
CREATE OR REPLACE STAGE raw_data
    DIRECTORY = (ENABLE = TRUE)
    COMMENT = 'Stage for raw data files';

CREATE OR REPLACE STAGE crm_exports
    DIRECTORY = (ENABLE = TRUE)
    COMMENT = 'Stage for CRM system exports';

CREATE OR REPLACE STAGE ml_models
    DIRECTORY = (ENABLE = TRUE)
    COMMENT = 'Stage for ML model artifacts';

-- =====================================================
-- FILE FORMATS
-- =====================================================

CREATE OR REPLACE FILE FORMAT csv_format
    TYPE = 'CSV'
    FIELD_DELIMITER = ','
    RECORD_DELIMITER = '\n'
    SKIP_HEADER = 1
    FIELD_OPTIONALLY_ENCLOSED_BY = '"'
    TRIM_SPACE = TRUE
    ERROR_ON_COLUMN_COUNT_MISMATCH = FALSE
    REPLACE_INVALID_CHARACTERS = TRUE
    EMPTY_FIELD_AS_NULL = TRUE
    NULL_IF = ('NULL', 'null', '', 'N/A', 'n/a');

CREATE OR REPLACE FILE FORMAT json_format
    TYPE = 'JSON'
    STRIP_OUTER_ARRAY = TRUE
    REPLACE_INVALID_CHARACTERS = TRUE
    DATE_FORMAT = 'AUTO'
    TIME_FORMAT = 'AUTO'
    TIMESTAMP_FORMAT = 'AUTO';

-- =====================================================
-- CORE CRM TABLES
-- =====================================================

USE SCHEMA salesgenie_ai.crm_data;

-- Companies/Accounts table
CREATE OR REPLACE TABLE companies (
    company_id STRING PRIMARY KEY,
    company_name STRING NOT NULL,
    industry STRING,
    company_size STRING,
    annual_revenue NUMBER(15,2),
    website STRING,
    phone STRING,
    address STRING,
    city STRING,
    state STRING,
    country STRING,
    postal_code STRING,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    created_by STRING,
    updated_by STRING,
    is_active BOOLEAN DEFAULT TRUE
);

-- Contacts table
CREATE OR REPLACE TABLE contacts (
    contact_id STRING PRIMARY KEY,
    company_id STRING,
    first_name STRING NOT NULL,
    last_name STRING NOT NULL,
    email STRING,
    phone STRING,
    mobile_phone STRING,
    job_title STRING,
    department STRING,
    decision_maker BOOLEAN DEFAULT FALSE,
    linkedin_url STRING,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    created_by STRING,
    updated_by STRING,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (company_id) REFERENCES companies(company_id)
);

-- Leads table
CREATE OR REPLACE TABLE leads (
    lead_id STRING PRIMARY KEY,
    company_id STRING,
    contact_id STRING,
    lead_source STRING,
    lead_status STRING,
    lead_score NUMBER(5,2),
    qualification_level STRING,
    estimated_value NUMBER(15,2),
    estimated_close_date DATE,
    assigned_to STRING,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    last_contacted_date TIMESTAMP_NTZ,
    next_follow_up_date TIMESTAMP_NTZ,
    notes TEXT,
    is_converted BOOLEAN DEFAULT FALSE,
    converted_date TIMESTAMP_NTZ,
    FOREIGN KEY (company_id) REFERENCES companies(company_id),
    FOREIGN KEY (contact_id) REFERENCES contacts(contact_id)
);

-- Opportunities table
CREATE OR REPLACE TABLE opportunities (
    opportunity_id STRING PRIMARY KEY,
    company_id STRING NOT NULL,
    contact_id STRING,
    lead_id STRING,
    opportunity_name STRING NOT NULL,
    stage STRING NOT NULL,
    probability NUMBER(5,2),
    amount NUMBER(15,2),
    expected_close_date DATE,
    actual_close_date DATE,
    sales_rep STRING NOT NULL,
    sales_manager STRING,
    product_category STRING,
    competitor STRING,
    loss_reason STRING,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    is_won BOOLEAN DEFAULT FALSE,
    is_lost BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (company_id) REFERENCES companies(company_id),
    FOREIGN KEY (contact_id) REFERENCES contacts(contact_id),
    FOREIGN KEY (lead_id) REFERENCES leads(lead_id)
);

-- Products table
CREATE OR REPLACE TABLE products (
    product_id STRING PRIMARY KEY,
    product_name STRING NOT NULL,
    product_category STRING,
    product_line STRING,
    unit_price NUMBER(15,2),
    cost NUMBER(15,2),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Opportunity Products (many-to-many relationship)
CREATE OR REPLACE TABLE opportunity_products (
    opportunity_id STRING,
    product_id STRING,
    quantity NUMBER(10,2),
    unit_price NUMBER(15,2),
    discount_percent NUMBER(5,2) DEFAULT 0,
    total_amount NUMBER(15,2),
    PRIMARY KEY (opportunity_id, product_id),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (product_id) REFERENCES products(product_id)
);

-- Activities table (calls, emails, meetings, etc.)
CREATE OR REPLACE TABLE activities (
    activity_id STRING PRIMARY KEY,
    activity_type STRING NOT NULL, -- 'call', 'email', 'meeting', 'task', 'note'
    subject STRING,
    description TEXT,
    activity_date TIMESTAMP_NTZ,
    duration_minutes NUMBER(10,2),
    company_id STRING,
    contact_id STRING,
    opportunity_id STRING,
    lead_id STRING,
    assigned_to STRING,
    completed BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    created_by STRING,
    FOREIGN KEY (company_id) REFERENCES companies(company_id),
    FOREIGN KEY (contact_id) REFERENCES contacts(contact_id),
    FOREIGN KEY (opportunity_id) REFERENCES opportunities(opportunity_id),
    FOREIGN KEY (lead_id) REFERENCES leads(lead_id)
);

-- Sales team/users table
CREATE OR REPLACE TABLE sales_users (
    user_id STRING PRIMARY KEY,
    username STRING UNIQUE NOT NULL,
    first_name STRING,
    last_name STRING,
    email STRING,
    role STRING, -- 'sales_rep', 'sales_manager', 'admin'
    territory STRING,
    manager_id STRING,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    FOREIGN KEY (manager_id) REFERENCES sales_users(user_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Create search optimization for key tables
ALTER TABLE companies ADD SEARCH OPTIMIZATION;
ALTER TABLE contacts ADD SEARCH OPTIMIZATION;
ALTER TABLE leads ADD SEARCH OPTIMIZATION;
ALTER TABLE opportunities ADD SEARCH OPTIMIZATION;
ALTER TABLE activities ADD SEARCH OPTIMIZATION;

-- =====================================================
-- INITIAL CONFIGURATION DATA
-- =====================================================

-- Insert sample sales users
INSERT INTO sales_users (user_id, username, first_name, last_name, email, role, territory) VALUES
('user_001', 'john.smith', 'John', 'Smith', '<EMAIL>', 'sales_manager', 'North America'),
('user_002', 'sarah.johnson', 'Sarah', 'Johnson', '<EMAIL>', 'sales_rep', 'West Coast'),
('user_003', 'mike.davis', 'Mike', 'Davis', '<EMAIL>', 'sales_rep', 'East Coast'),
('user_004', 'lisa.wilson', 'Lisa', 'Wilson', '<EMAIL>', 'sales_rep', 'Central'),
('user_005', 'admin', 'System', 'Admin', '<EMAIL>', 'admin', 'All');

-- Insert sample product categories
INSERT INTO products (product_id, product_name, product_category, product_line, unit_price, cost, description) VALUES
('prod_001', 'Enterprise CRM Software', 'Software', 'Enterprise Solutions', 50000.00, 15000.00, 'Comprehensive CRM solution for large enterprises'),
('prod_002', 'Sales Analytics Platform', 'Software', 'Analytics', 25000.00, 8000.00, 'Advanced sales analytics and reporting platform'),
('prod_003', 'Mobile Sales App', 'Software', 'Mobile Solutions', 15000.00, 5000.00, 'Mobile application for field sales teams'),
('prod_004', 'Integration Services', 'Services', 'Professional Services', 10000.00, 3000.00, 'Custom integration and implementation services'),
('prod_005', 'Training & Support', 'Services', 'Support', 5000.00, 1500.00, 'Comprehensive training and ongoing support');

COMMIT;
