#!/usr/bin/env python3
"""
Cleanup script to remove incorrectly placed tables and recreate schemas properly
"""

import snowflake.connector
import json
import getpass

def main():
    # Load config
    with open('config/snowflake_config.json', 'r') as f:
        config = json.load(f)
    
    password = getpass.getpass(f"Enter password for {config['user']}: ")
    
    conn = snowflake.connector.connect(
        account=config['account'],
        user=config['user'],
        password=password,
        role=config['role'],
        warehouse=config['warehouse'],
        database=config['database']
    )
    
    cursor = conn.cursor()
    
    print("🧹 CLEANING UP INCORRECTLY PLACED TABLES...")
    
    # Clean up staging tables from wrong schemas
    schemas_to_clean = ['ANALYTICS', 'AI_SERVICES', 'CRM_DATA']
    staging_tables = ['STAGING_ACTIVITIES', 'STAGING_CONTACTS', 'STAGING_OPPORTUNITIES']
    
    for schema in schemas_to_clean:
        print(f"\nCleaning schema: {schema}")
        cursor.execute(f"USE SCHEMA salesgenie_ai.{schema}")
        
        for table in staging_tables:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"  ✅ Dropped {table} from {schema}")
            except Exception as e:
                print(f"  ⚠️  Could not drop {table} from {schema}: {e}")
    
    # Clean up any other incorrectly placed tables
    analytics_tables = [
        'SALES_FORECASTS', 'HISTORICAL_METRICS', 'CHURN_PREDICTIONS', 
        'LEAD_SCORING_MODELS', 'PERFORMANCE_BENCHMARKS'
    ]
    
    ai_services_tables = [
        'AI_MODEL_CONFIGS', 'CORTEX_USAGE_LOGS', 'TRAINING_DATASETS',
        'MODEL_PERFORMANCE', 'AI_RECOMMENDATIONS', 'SEARCH_CONFIGURATIONS',
        'CONVERSATION_HISTORY'
    ]
    
    integration_tables = [
        'EXTERNAL_SYSTEMS', 'SYNC_LOGS', 'FIELD_MAPPINGS',
        'API_QUOTAS', 'WEBHOOK_CONFIGS', 'WEBHOOK_EVENTS'
    ]
    
    # Clean analytics tables from wrong schemas
    for schema in ['AI_SERVICES', 'INTEGRATION', 'CRM_DATA']:
        print(f"\nCleaning analytics tables from {schema}")
        cursor.execute(f"USE SCHEMA salesgenie_ai.{schema}")
        for table in analytics_tables:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"  ✅ Dropped {table} from {schema}")
            except Exception as e:
                print(f"  ⚠️  Could not drop {table} from {schema}: {e}")
    
    # Clean AI services tables from wrong schemas
    for schema in ['ANALYTICS', 'INTEGRATION', 'CRM_DATA']:
        print(f"\nCleaning AI services tables from {schema}")
        cursor.execute(f"USE SCHEMA salesgenie_ai.{schema}")
        for table in ai_services_tables:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"  ✅ Dropped {table} from {schema}")
            except Exception as e:
                print(f"  ⚠️  Could not drop {table} from {schema}: {e}")
    
    # Clean integration tables from wrong schemas
    for schema in ['ANALYTICS', 'AI_SERVICES', 'CRM_DATA']:
        print(f"\nCleaning integration tables from {schema}")
        cursor.execute(f"USE SCHEMA salesgenie_ai.{schema}")
        for table in integration_tables:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"  ✅ Dropped {table} from {schema}")
            except Exception as e:
                print(f"  ⚠️  Could not drop {table} from {schema}: {e}")
    
    print("\n✅ CLEANUP COMPLETED!")
    print("\nNow run: python populate_all_schemas.py")
    
    conn.close()

if __name__ == "__main__":
    main()
