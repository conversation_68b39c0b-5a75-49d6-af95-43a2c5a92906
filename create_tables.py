#!/usr/bin/env python3
"""
SalesGenie AI - Table Creation Script
Fix for missing tables issue
"""

import os
import sys
import json
import getpass
import snowflake.connector
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_snowflake_connection():
    """Get Snowflake connection with credentials"""
    
    # Load config
    config_path = "config/snowflake_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {
            "account": "GNBXJLF-PKB97538",
            "user": "ASHUTOSH1",
            "warehouse": "SALESGENIE_AI_WH",
            "database": "SALESGENIE_AI",
            "schema": "CRM_DATA",
            "role": "ACCOUNTADMIN"
        }
    
    # Get password securely
    if config.get("password") == "XXXXXXXXXXXXXXXXXX" or not config.get("password"):
        password = getpass.getpass(f"Enter password for {config['user']}: ")
    else:
        password = config["password"]
    
    try:
        logger.info("Connecting to Snowflake...")
        connection = snowflake.connector.connect(
            account=config["account"],
            user=config["user"],
            password=password,
            role=config["role"],
            warehouse=config["warehouse"],
            database=config["database"],
            schema=config["schema"]
        )
        logger.info("Successfully connected to Snowflake")
        return connection
    except Exception as e:
        logger.error(f"Failed to connect to Snowflake: {str(e)}")
        return None

def create_tables(connection):
    """Create all CRM tables"""
    cursor = connection.cursor()
    
    try:
        # Set context
        cursor.execute("USE ROLE ACCOUNTADMIN")
        cursor.execute("USE WAREHOUSE salesgenie_ai_wh")
        cursor.execute("USE DATABASE salesgenie_ai")
        cursor.execute("USE SCHEMA crm_data")
        
        logger.info("Creating tables...")
        
        # Companies table
        logger.info("Creating companies table...")
        cursor.execute("""
            CREATE OR REPLACE TABLE companies (
                company_id STRING PRIMARY KEY,
                company_name STRING NOT NULL,
                industry STRING,
                company_size STRING,
                annual_revenue NUMBER(15,2),
                website STRING,
                phone STRING,
                address STRING,
                city STRING,
                state STRING,
                country STRING,
                postal_code STRING,
                created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
                updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
                created_by STRING,
                updated_by STRING,
                is_active BOOLEAN DEFAULT TRUE
            )
        """)
        logger.info("✅ Companies table created")
        
        # Contacts table
        logger.info("Creating contacts table...")
        cursor.execute("""
            CREATE OR REPLACE TABLE contacts (
                contact_id STRING PRIMARY KEY,
                company_id STRING,
                first_name STRING NOT NULL,
                last_name STRING NOT NULL,
                email STRING,
                phone STRING,
                mobile_phone STRING,
                job_title STRING,
                department STRING,
                decision_maker BOOLEAN DEFAULT FALSE,
                linkedin_url STRING,
                created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
                updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
                created_by STRING,
                updated_by STRING,
                is_active BOOLEAN DEFAULT TRUE
            )
        """)
        logger.info("✅ Contacts table created")
        
        # Leads table
        logger.info("Creating leads table...")
        cursor.execute("""
            CREATE OR REPLACE TABLE leads (
                lead_id STRING PRIMARY KEY,
                company_id STRING,
                contact_id STRING,
                lead_source STRING,
                lead_status STRING,
                lead_score NUMBER(5,2),
                qualification_level STRING,
                estimated_value NUMBER(15,2),
                estimated_close_date DATE,
                assigned_to STRING,
                created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
                updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
                last_contacted_date TIMESTAMP_NTZ,
                next_follow_up_date TIMESTAMP_NTZ,
                notes TEXT,
                is_converted BOOLEAN DEFAULT FALSE,
                converted_date TIMESTAMP_NTZ
            )
        """)
        logger.info("✅ Leads table created")
        
        # Opportunities table
        logger.info("Creating opportunities table...")
        cursor.execute("""
            CREATE OR REPLACE TABLE opportunities (
                opportunity_id STRING PRIMARY KEY,
                company_id STRING NOT NULL,
                contact_id STRING,
                lead_id STRING,
                opportunity_name STRING NOT NULL,
                stage STRING NOT NULL,
                probability NUMBER(5,2),
                amount NUMBER(15,2),
                expected_close_date DATE,
                actual_close_date DATE,
                sales_rep STRING NOT NULL,
                sales_manager STRING,
                product_category STRING,
                competitor STRING,
                loss_reason STRING,
                created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
                updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
                is_won BOOLEAN DEFAULT FALSE,
                is_lost BOOLEAN DEFAULT FALSE
            )
        """)
        logger.info("✅ Opportunities table created")
        
        # Products table
        logger.info("Creating products table...")
        cursor.execute("""
            CREATE OR REPLACE TABLE products (
                product_id STRING PRIMARY KEY,
                product_name STRING NOT NULL,
                product_category STRING,
                product_line STRING,
                unit_price NUMBER(15,2),
                cost NUMBER(15,2),
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
                updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
            )
        """)
        logger.info("✅ Products table created")
        
        # Activities table
        logger.info("Creating activities table...")
        cursor.execute("""
            CREATE OR REPLACE TABLE activities (
                activity_id STRING PRIMARY KEY,
                activity_type STRING NOT NULL,
                subject STRING,
                description TEXT,
                activity_date TIMESTAMP_NTZ,
                duration_minutes NUMBER(10,2),
                company_id STRING,
                contact_id STRING,
                opportunity_id STRING,
                lead_id STRING,
                assigned_to STRING,
                completed BOOLEAN DEFAULT FALSE,
                created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
                created_by STRING
            )
        """)
        logger.info("✅ Activities table created")
        
        # Sales users table
        logger.info("Creating sales_users table...")
        cursor.execute("""
            CREATE OR REPLACE TABLE sales_users (
                user_id STRING PRIMARY KEY,
                username STRING UNIQUE NOT NULL,
                first_name STRING,
                last_name STRING,
                email STRING,
                role STRING,
                territory STRING,
                manager_id STRING,
                is_active BOOLEAN DEFAULT TRUE,
                created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
            )
        """)
        logger.info("✅ Sales users table created")
        
        return True
        
    except Exception as e:
        logger.error(f"Error creating tables: {str(e)}")
        return False
    finally:
        cursor.close()

def insert_sample_data(connection):
    """Insert sample data into tables"""
    cursor = connection.cursor()
    
    try:
        cursor.execute("USE SCHEMA salesgenie_ai.crm_data")
        
        logger.info("Inserting sample data...")
        
        # Insert sample sales users first
        cursor.execute("""
            INSERT INTO sales_users (user_id, username, first_name, last_name, email, role, territory) VALUES
            ('user_001', 'john.smith', 'John', 'Smith', '<EMAIL>', 'sales_manager', 'North America'),
            ('user_002', 'sarah.johnson', 'Sarah', 'Johnson', '<EMAIL>', 'sales_rep', 'West Coast'),
            ('user_003', 'mike.davis', 'Mike', 'Davis', '<EMAIL>', 'sales_rep', 'East Coast'),
            ('user_004', 'lisa.wilson', 'Lisa', 'Wilson', '<EMAIL>', 'sales_rep', 'Central'),
            ('user_005', 'admin', 'System', 'Admin', '<EMAIL>', 'admin', 'All')
        """)
        logger.info("✅ Sample sales users inserted")
        
        # Insert sample products
        cursor.execute("""
            INSERT INTO products (product_id, product_name, product_category, product_line, unit_price, cost, description) VALUES
            ('prod_001', 'Enterprise CRM Software', 'Software', 'Enterprise Solutions', 50000.00, 15000.00, 'Comprehensive CRM solution for large enterprises'),
            ('prod_002', 'Sales Analytics Platform', 'Software', 'Analytics', 25000.00, 8000.00, 'Advanced sales analytics and reporting platform'),
            ('prod_003', 'Mobile Sales App', 'Software', 'Mobile Solutions', 15000.00, 5000.00, 'Mobile application for field sales teams'),
            ('prod_004', 'Integration Services', 'Services', 'Professional Services', 10000.00, 3000.00, 'Custom integration and implementation services'),
            ('prod_005', 'Training & Support', 'Services', 'Support', 5000.00, 1500.00, 'Comprehensive training and ongoing support')
        """)
        logger.info("✅ Sample products inserted")
        
        return True
        
    except Exception as e:
        logger.error(f"Error inserting sample data: {str(e)}")
        return False
    finally:
        cursor.close()

def main():
    """Main function"""
    logger.info("Starting table creation...")
    
    # Connect to Snowflake
    connection = get_snowflake_connection()
    if not connection:
        return False
    
    try:
        # Create tables
        if not create_tables(connection):
            logger.error("Failed to create tables")
            return False
        
        # Insert sample data
        if not insert_sample_data(connection):
            logger.error("Failed to insert sample data")
            return False
        
        logger.info("🎉 Tables created and sample data inserted successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Table creation failed: {str(e)}")
        return False
    
    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n" + "="*60)
        print("✅ TABLES CREATED SUCCESSFULLY!")
        print("="*60)
        print("\nNext step: Run 'python verify_deployment.py' to verify")
    else:
        print("\n" + "="*60)
        print("❌ TABLE CREATION FAILED")
        print("="*60)
        sys.exit(1)
