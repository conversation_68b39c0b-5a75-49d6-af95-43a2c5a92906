#!/usr/bin/env python3
"""
SalesGenie AI - Complete Schema Population Script
Nihilent x Snowflake Hackathon 2025

This script populates all schemas (CRM_DATA, ANALYTICS, AI_SERVICES, INTEGRATION)
with comprehensive sample data to demonstrate the full capabilities of SalesGenie AI.
"""

import os
import sys
import json
import getpass
import snowflake.connector
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_snowflake_connection():
    """Get Snowflake connection with credentials"""
    
    # Load config
    config_path = "config/snowflake_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {
            "account": "GNBXJLF-PKB97538",
            "user": "ASHUTOSH1",
            "warehouse": "salesgenie_ai_wh",
            "database": "salesgenie_ai",
            "schema": "crm_data",
            "role": "ACCOUNTADMIN"
        }
    
    # Get password securely
    if config.get("password") == "XXXXXXXXXXXXXXXXXX" or not config.get("password"):
        password = getpass.getpass(f"Enter password for {config['user']}: ")
    else:
        password = config["password"]
    
    try:
        logger.info("Connecting to Snowflake...")
        connection = snowflake.connector.connect(
            account=config["account"],
            user=config["user"],
            password=password,
            role=config["role"],
            warehouse=config["warehouse"],
            database=config["database"],
            schema=config["schema"]
        )
        logger.info("Successfully connected to Snowflake")
        return connection
    except Exception as e:
        logger.error(f"Failed to connect to Snowflake: {str(e)}")
        return None

def execute_sql_file(connection, file_path):
    """Execute SQL commands from a file"""
    cursor = connection.cursor()
    
    try:
        logger.info(f"Executing SQL file: {file_path}")
        
        with open(file_path, 'r') as f:
            sql_content = f.read()
        
        # Split by semicolon and execute each statement
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements):
            if statement.upper().startswith(('CREATE', 'INSERT', 'UPDATE', 'DELETE', 'USE', 'COMMIT')):
                try:
                    cursor.execute(statement)
                    logger.debug(f"Executed statement {i+1}/{len(statements)}")
                except Exception as e:
                    logger.warning(f"Statement {i+1} failed: {str(e)}")
                    # Continue with next statement
        
        logger.info(f"✅ Successfully executed {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error executing {file_path}: {str(e)}")
        return False
    finally:
        cursor.close()

def populate_schemas(connection):
    """Populate all schemas with data"""
    
    # List of SQL files to execute in order
    sql_files = [
        "sql/01_setup_database.sql",
        "sql/02_sample_data.sql", 
        "sql/03_ai_services_setup.sql",
        "sql/04_advanced_analytics.sql",
        "sql/05_integration_schema.sql",
        "sql/06_ai_services_data.sql"
    ]
    
    success_count = 0
    total_files = len(sql_files)
    
    for sql_file in sql_files:
        if os.path.exists(sql_file):
            if execute_sql_file(connection, sql_file):
                success_count += 1
            else:
                logger.error(f"Failed to execute {sql_file}")
        else:
            logger.warning(f"SQL file not found: {sql_file}")
    
    logger.info(f"Schema population completed: {success_count}/{total_files} files executed successfully")
    return success_count == total_files

def verify_data_population(connection):
    """Verify that data has been populated in all schemas"""
    cursor = connection.cursor()
    
    verification_queries = [
        # CRM_DATA schema
        ("CRM_DATA Companies", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.companies"),
        ("CRM_DATA Opportunities", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.opportunities"),
        ("CRM_DATA Leads", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.leads"),
        ("CRM_DATA Activities", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.activities"),
        ("CRM_DATA Sales Users", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.sales_users"),
        
        # ANALYTICS schema
        ("ANALYTICS Forecasts", "SELECT COUNT(*) FROM salesgenie_ai.analytics.sales_forecasts"),
        ("ANALYTICS Metrics", "SELECT COUNT(*) FROM salesgenie_ai.analytics.historical_metrics"),
        ("ANALYTICS Churn Predictions", "SELECT COUNT(*) FROM salesgenie_ai.analytics.churn_predictions"),
        ("ANALYTICS Benchmarks", "SELECT COUNT(*) FROM salesgenie_ai.analytics.performance_benchmarks"),
        
        # AI_SERVICES schema
        ("AI_SERVICES Model Configs", "SELECT COUNT(*) FROM salesgenie_ai.ai_services.ai_model_configs"),
        ("AI_SERVICES Usage Logs", "SELECT COUNT(*) FROM salesgenie_ai.ai_services.cortex_usage_logs"),
        ("AI_SERVICES Training Data", "SELECT COUNT(*) FROM salesgenie_ai.ai_services.training_datasets"),
        ("AI_SERVICES Recommendations", "SELECT COUNT(*) FROM salesgenie_ai.ai_services.ai_recommendations"),
        
        # INTEGRATION schema
        ("INTEGRATION External Systems", "SELECT COUNT(*) FROM salesgenie_ai.integration.external_systems"),
        ("INTEGRATION Sync Logs", "SELECT COUNT(*) FROM salesgenie_ai.integration.sync_logs"),
        ("INTEGRATION Staging Companies", "SELECT COUNT(*) FROM salesgenie_ai.integration.staging_companies"),
    ]
    
    print("\n🔍 Data Population Verification:")
    print("=" * 60)
    
    all_verified = True
    
    for description, query in verification_queries:
        try:
            cursor.execute(query)
            count = cursor.fetchone()[0]
            status = "✅" if count > 0 else "❌"
            print(f"{status} {description}: {count} records")
            if count == 0:
                all_verified = False
        except Exception as e:
            print(f"❌ {description}: Error - {str(e)}")
            all_verified = False
    
    cursor.close()
    return all_verified

def main():
    """Main function"""
    print("=" * 60)
    print("🚀 SALESGENIE AI - COMPLETE SCHEMA POPULATION")
    print("=" * 60)
    print("This script will populate ALL schemas with comprehensive data:")
    print("• CRM_DATA - Core sales data")
    print("• ANALYTICS - Advanced analytics and ML models")
    print("• AI_SERVICES - AI configurations and usage tracking")
    print("• INTEGRATION - External system integrations")
    print("=" * 60)
    
    # Connect to Snowflake
    connection = get_snowflake_connection()
    if not connection:
        logger.error("Cannot proceed without Snowflake connection")
        return False
    
    try:
        # Populate all schemas
        logger.info("Starting schema population...")
        if populate_schemas(connection):
            logger.info("✅ All schemas populated successfully!")
            
            # Verify data population
            logger.info("Verifying data population...")
            if verify_data_population(connection):
                print("\n" + "=" * 60)
                print("✅ SCHEMA POPULATION COMPLETED SUCCESSFULLY!")
                print("=" * 60)
                print("\n🎉 Your SalesGenie AI solution now has comprehensive data across all schemas!")
                print("\nWhat's been populated:")
                print("• 📊 CRM Data: Companies, contacts, leads, opportunities, activities")
                print("• 📈 Analytics: Forecasts, metrics, churn predictions, benchmarks")
                print("• 🤖 AI Services: Model configs, usage logs, training data, recommendations")
                print("• 🔗 Integration: External systems, sync logs, staging data")
                print("\nNext steps:")
                print("1. Run 'python verify_deployment.py' to verify everything")
                print("2. Test the Streamlit app: 'streamlit run streamlit_app.py'")
                print("3. Try advanced analytics queries on all schemas")
                return True
            else:
                logger.warning("Some data verification checks failed")
                return False
        else:
            logger.error("Schema population failed")
            return False
        
    except Exception as e:
        logger.error(f"Schema population failed: {str(e)}")
        return False
    
    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n" + "=" * 60)
        print("❌ SCHEMA POPULATION FAILED")
        print("=" * 60)
        print("Please check the errors above and try again")
        sys.exit(1)
