#!/usr/bin/env python3
"""
Test script to verify Streamlit app functionality
"""

import subprocess
import sys
import time
import webbrowser
import threading

def test_streamlit_app():
    """Test the Streamlit application"""
    print("🧪 Testing SalesGenie AI Streamlit App...")

    try:
        # Start Streamlit app in a separate process
        print("🚀 Starting Streamlit app...")
        print("📝 Note: If you see password warnings, run 'python update_password.py' first")
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501",
            "--server.headless", "false"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # Wait a moment for the app to start
        time.sleep(5)

        # Check if process is running
        if process.poll() is None:
            print("✅ Streamlit app started successfully!")
            print("🌐 App should be available at: http://localhost:8501")

            # Try to open browser
            try:
                webbrowser.open("http://localhost:8501")
                print("🔗 Opened app in browser")
            except:
                print("ℹ️  Please manually open http://localhost:8501 in your browser")

            print("\n📋 Test Instructions:")
            print("1. Check if the app loads without errors")
            print("2. Try the sample questions:")
            print("   - 'Show me my sales pipeline breakdown by stage'")
            print("   - 'Analyze my lead performance and scoring'")
            print("   - 'Show me sales team performance'")
            print("3. Test the quick action buttons in the sidebar")
            print("4. Verify the metrics display correctly")

            print("\n⏹️  Press Ctrl+C to stop the test")

            try:
                # Keep the process running
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping Streamlit app...")
                process.terminate()
                process.wait()
                print("✅ App stopped successfully")

        else:
            stdout, stderr = process.communicate()
            print("❌ Failed to start Streamlit app")
            print(f"Error: {stderr.decode()}")
            return False

    except Exception as e:
        print(f"❌ Error testing Streamlit app: {str(e)}")
        return False

    return True

if __name__ == "__main__":
    print("="*60)
    print("🧪 SALESGENIE AI STREAMLIT TEST")
    print("="*60)

    success = test_streamlit_app()

    if success:
        print("\n" + "="*60)
        print("✅ STREAMLIT TEST COMPLETED")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ STREAMLIT TEST FAILED")
        print("="*60)
        sys.exit(1)
