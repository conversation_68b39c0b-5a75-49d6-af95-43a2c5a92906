#!/usr/bin/env python3
"""
SalesGenie AI - Add Comprehensive Sample Data
"""

import os
import sys
import json
import getpass
import snowflake.connector
import logging
from datetime import datetime, timedelta
import random

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_snowflake_connection():
    """Get Snowflake connection with credentials"""

    # Load config
    config_path = "config/snowflake_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {
            "account": "GNBXJLF-PKB97538",
            "user": "ASHUTOSH1",
            "warehouse": "SALESGENIE_AI_WH",
            "database": "SALESGENIE_AI",
            "schema": "CRM_DATA",
            "role": "ACCOUNTADMIN"
        }

    # Get password securely
    if config.get("password") == "XXXXXXXXXXXXXXXXXX" or not config.get("password"):
        password = getpass.getpass(f"Enter password for {config['user']}: ")
    else:
        password = config["password"]

    try:
        logger.info("Connecting to Snowflake...")
        connection = snowflake.connector.connect(
            account=config["account"],
            user=config["user"],
            password=password,
            role=config["role"],
            warehouse=config["warehouse"],
            database=config["database"],
            schema=config["schema"]
        )
        logger.info("Successfully connected to Snowflake")
        return connection
    except Exception as e:
        logger.error(f"Failed to connect to Snowflake: {str(e)}")
        return None

def add_sample_data(connection):
    """Add comprehensive sample data"""
    cursor = connection.cursor()

    try:
        cursor.execute("USE SCHEMA salesgenie_ai.crm_data")

        logger.info("Adding comprehensive sample data...")

        # Insert sample companies
        logger.info("Inserting sample companies...")
        cursor.execute("""
            INSERT INTO companies (company_id, company_name, industry, company_size, annual_revenue, website, phone, address, city, state, country, postal_code, created_by) VALUES
            ('comp_001', 'TechCorp Solutions', 'Technology', 'Large (500-999)', 25000000.00, 'www.techcorp.com', '******-0101', '123 Tech Street', 'San Francisco', 'CA', 'USA', '94105', 'admin'),
            ('comp_002', 'Global Manufacturing Inc', 'Manufacturing', 'Enterprise (1000+)', *********.00, 'www.globalmfg.com', '******-0102', '456 Industrial Blvd', 'Detroit', 'MI', 'USA', '48201', 'admin'),
            ('comp_003', 'HealthTech Innovations', 'Healthcare', 'Medium (100-499)', 8000000.00, 'www.healthtech.com', '******-0103', '789 Medical Center Dr', 'Boston', 'MA', 'USA', '02101', 'admin'),
            ('comp_004', 'Financial Services Group', 'Financial Services', 'Large (500-999)', 45000000.00, 'www.finservices.com', '******-0104', '321 Wall Street', 'New York', 'NY', 'USA', '10005', 'admin'),
            ('comp_005', 'Retail Chain Corp', 'Retail', 'Enterprise (1000+)', 75000000.00, 'www.retailchain.com', '******-0105', '654 Commerce Ave', 'Chicago', 'IL', 'USA', '60601', 'admin')
        """)
        logger.info("✅ Sample companies inserted")

        # Insert sample contacts
        logger.info("Inserting sample contacts...")
        cursor.execute("""
            INSERT INTO contacts (contact_id, company_id, first_name, last_name, email, phone, job_title, department, decision_maker, created_by) VALUES
            ('cont_001', 'comp_001', 'Alice', 'Johnson', '<EMAIL>', '******-1001', 'CTO', 'Technology', TRUE, 'admin'),
            ('cont_002', 'comp_002', 'Bob', 'Smith', '<EMAIL>', '******-1002', 'VP Operations', 'Operations', TRUE, 'admin'),
            ('cont_003', 'comp_003', 'Carol', 'Davis', '<EMAIL>', '******-1003', 'Director IT', 'IT', TRUE, 'admin'),
            ('cont_004', 'comp_004', 'David', 'Wilson', '<EMAIL>', '******-1004', 'CFO', 'Finance', TRUE, 'admin'),
            ('cont_005', 'comp_005', 'Eva', 'Brown', '<EMAIL>', '******-1005', 'Head of Digital', 'Marketing', TRUE, 'admin'),
            ('cont_006', 'comp_001', 'Frank', 'Miller', '<EMAIL>', '******-1006', 'IT Manager', 'IT', FALSE, 'admin'),
            ('cont_007', 'comp_002', 'Grace', 'Taylor', '<EMAIL>', '******-1007', 'Project Manager', 'Operations', FALSE, 'admin'),
            ('cont_008', 'comp_003', 'Henry', 'Anderson', '<EMAIL>', '******-1008', 'Software Engineer', 'IT', FALSE, 'admin')
        """)
        logger.info("✅ Sample contacts inserted")

        # Insert sample leads
        logger.info("Inserting sample leads...")
        cursor.execute("""
            INSERT INTO leads (lead_id, company_id, contact_id, lead_source, lead_status, lead_score, qualification_level, estimated_value, estimated_close_date, assigned_to, last_contacted_date, next_follow_up_date, notes) VALUES
            ('lead_001', 'comp_001', 'cont_006', 'Website', 'Qualified', 85.5, 'Hot', 75000.00, '2025-07-15', 'user_002', '2025-05-25', '2025-06-01', 'Interested in enterprise CRM solution'),
            ('lead_002', 'comp_002', 'cont_007', 'Trade Show', 'Contacted', 65.0, 'Warm', 120000.00, '2025-08-30', 'user_003', '2025-05-20', '2025-06-05', 'Met at manufacturing expo, needs integration'),
            ('lead_003', 'comp_003', 'cont_008', 'Referral', 'New', 90.2, 'Hot', 45000.00, '2025-06-20', 'user_002', '2025-05-28', '2025-05-30', 'Referred by existing customer'),
            ('lead_004', 'comp_004', NULL, 'Cold Call', 'Contacted', 45.0, 'Cold', 95000.00, '2025-09-15', 'user_004', '2025-05-15', '2025-06-10', 'Initial interest, needs more qualification'),
            ('lead_005', 'comp_005', 'cont_005', 'LinkedIn', 'Qualified', 75.8, 'Warm', 85000.00, '2025-07-01', 'user_003', '2025-05-22', '2025-06-03', 'Connected on LinkedIn, scheduling demo'),
            ('lead_006', NULL, NULL, 'Email Campaign', 'New', 55.0, 'Warm', 35000.00, '2025-08-01', 'user_004', '2025-05-26', '2025-06-02', 'Responded to email campaign')
        """)
        logger.info("✅ Sample leads inserted")

        # Insert sample opportunities
        logger.info("Inserting sample opportunities...")
        cursor.execute("""
            INSERT INTO opportunities (opportunity_id, company_id, contact_id, opportunity_name, stage, probability, amount, expected_close_date, sales_rep, sales_manager, product_category) VALUES
            ('opp_001', 'comp_001', 'cont_001', 'TechCorp CRM Implementation', 'Discovery', 25.0, 85000.00, '2025-08-15', 'user_002', 'user_001', 'Software'),
            ('opp_002', 'comp_002', 'cont_002', 'Global Mfg Analytics Platform', 'Qualification', 40.0, 150000.00, '2025-09-30', 'user_003', 'user_001', 'Software'),
            ('opp_003', 'comp_003', 'cont_003', 'HealthTech Mobile Solution', 'Proposal', 65.0, 65000.00, '2025-07-20', 'user_002', 'user_001', 'Software'),
            ('opp_004', 'comp_004', 'cont_004', 'FinServices Integration', 'Negotiation', 80.0, 95000.00, '2025-06-30', 'user_004', 'user_001', 'Services'),
            ('opp_005', 'comp_005', 'cont_005', 'Retail Chain Training', 'Discovery', 30.0, 25000.00, '2025-08-01', 'user_003', 'user_001', 'Services'),
            ('opp_006', 'comp_001', 'cont_006', 'TechCorp Support Package', 'Proposal', 70.0, 35000.00, '2025-07-10', 'user_002', 'user_001', 'Services'),
            ('opp_007', 'comp_002', 'cont_007', 'Global Mfg Enterprise License', 'Qualification', 45.0, 200000.00, '2025-10-15', 'user_003', 'user_001', 'Software'),
            ('opp_008', 'comp_004', 'cont_004', 'FinServices Consulting', 'Negotiation', 75.0, 45000.00, '2025-06-15', 'user_004', 'user_001', 'Services')
        """)
        logger.info("✅ Sample opportunities inserted")

        # Insert sample activities
        logger.info("Inserting sample activities...")
        cursor.execute("""
            INSERT INTO activities (activity_id, activity_type, subject, description, activity_date, duration_minutes, company_id, contact_id, opportunity_id, assigned_to, completed, created_by) VALUES
            ('act_001', 'call', 'Discovery Call - TechCorp', 'Initial discovery call to understand requirements', '2025-05-25 14:00:00', 60, 'comp_001', 'cont_001', 'opp_001', 'user_002', TRUE, 'user_002'),
            ('act_002', 'email', 'Follow-up Email - Global Mfg', 'Sent proposal and pricing information', '2025-05-26 09:30:00', 15, 'comp_002', 'cont_002', 'opp_002', 'user_003', TRUE, 'user_003'),
            ('act_003', 'meeting', 'Demo - HealthTech', 'Product demonstration and Q&A session', '2025-05-27 15:00:00', 90, 'comp_003', 'cont_003', 'opp_003', 'user_002', TRUE, 'user_002'),
            ('act_004', 'call', 'Negotiation Call - FinServices', 'Discussing contract terms and pricing', '2025-05-28 11:00:00', 45, 'comp_004', 'cont_004', 'opp_004', 'user_004', TRUE, 'user_004'),
            ('act_005', 'task', 'Prepare Proposal - Retail Chain', 'Create customized proposal for training services', '2025-05-29 10:00:00', 120, 'comp_005', 'cont_005', 'opp_005', 'user_003', FALSE, 'user_003'),
            ('act_006', 'meeting', 'Contract Review - TechCorp Support', 'Review support package contract details', '2025-05-30 14:00:00', 60, 'comp_001', 'cont_006', 'opp_006', 'user_002', FALSE, 'user_002')
        """)
        logger.info("✅ Sample activities inserted")

        return True

    except Exception as e:
        logger.error(f"Error adding sample data: {str(e)}")
        return False
    finally:
        cursor.close()

def main():
    """Main function"""
    logger.info("Starting sample data insertion...")

    # Connect to Snowflake
    connection = get_snowflake_connection()
    if not connection:
        return False

    try:
        # Add sample data
        if not add_sample_data(connection):
            logger.error("Failed to add sample data")
            return False

        logger.info("🎉 Sample data added successfully!")
        return True

    except Exception as e:
        logger.error(f"Sample data insertion failed: {str(e)}")
        return False

    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n" + "="*60)
        print("✅ SAMPLE DATA ADDED SUCCESSFULLY!")
        print("="*60)
        print("\nNext step: Run 'python verify_deployment.py' to verify")
    else:
        print("\n" + "="*60)
        print("❌ SAMPLE DATA INSERTION FAILED")
        print("="*60)
        sys.exit(1)
