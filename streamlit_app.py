"""
SalesGenie AI - Conversational AI for Sales Excellence
Nihilent x Snowflake Hackathon 2025

Main Streamlit application providing conversational interface to sales CRM data
using Snowflake Cortex AI services.
"""

import streamlit as st

# Page configuration - MUST be first Streamlit command
st.set_page_config(
    page_title="SalesGenie AI",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

import json
import pandas as pd
from typing import List, Dict, Optional, Tuple
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import os
import sys

# Snowflake imports with error handling
try:
    import snowflake.connector
    from snowflake.snowpark import Session
    # Note: snowflake.cortex might not be available in all versions
    try:
        from snowflake.cortex import Complete, Sentiment, Summarize, ExtractAnswer
        CORTEX_AVAILABLE = True
    except ImportError as e:
        CORTEX_AVAILABLE = False
    SNOWFLAKE_AVAILABLE = True
except ImportError as e:
    SNOWFLAKE_AVAILABLE = False
    CORTEX_AVAILABLE = False

import requests

# Custom CSS for better UI
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .ai-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .sidebar .sidebar-content {
        background-color: #f8f9fa;
    }
</style>
""", unsafe_allow_html=True)

# Constants
API_ENDPOINT = "/api/v2/cortex/analyst/message"
API_TIMEOUT = 30000  # 30 seconds
SEMANTIC_MODEL_PATH = "salesgenie_ai.ai_services.sales_crm_semantic_model"

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []
if "snowflake_session" not in st.session_state:
    st.session_state.snowflake_session = None

def init_snowflake_connection():
    """Initialize Snowflake connection"""
    if not SNOWFLAKE_AVAILABLE:
        st.warning("Snowflake packages not available. Using demo mode.")
        return None

    if not CORTEX_AVAILABLE:
        st.info("ℹ️ Snowflake Cortex not available. Using standard Snowflake connection with demo AI responses.")

    try:
        # Try to load from config file
        config_path = "config/snowflake_config.json"
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
            connection_params = {
                "account": config.get("account", "GNBXJLF-PKB97538"),
                "user": config.get("user", "ASHUTOSH1"),
                "password": config.get("password", "your_password"),
                "warehouse": config.get("warehouse", "salesgenie_ai_wh"),
                "database": config.get("database", "salesgenie_ai"),
                "schema": config.get("schema", "crm_data"),
                "role": config.get("role", "ACCOUNTADMIN")
            }
        else:
            # Fallback to environment variables or default values
            connection_params = {
                "account": os.getenv("SNOWFLAKE_ACCOUNT", "GNBXJLF-PKB97538"),
                "user": os.getenv("SNOWFLAKE_USER", "ASHUTOSH1"),
                "password": os.getenv("SNOWFLAKE_PASSWORD", "your_password"),
                "warehouse": os.getenv("SNOWFLAKE_WAREHOUSE", "salesgenie_ai_wh"),
                "database": os.getenv("SNOWFLAKE_DATABASE", "salesgenie_ai"),
                "schema": os.getenv("SNOWFLAKE_SCHEMA", "crm_data"),
                "role": os.getenv("SNOWFLAKE_ROLE", "ACCOUNTADMIN")
            }

        # Check if we have valid credentials
        if connection_params["password"] == "your_password" or connection_params["password"] == "XXXXXXXXXXXXXXXXXX":
            st.warning("⚠️ Snowflake password not configured. Using demo mode with sample data.")
            return None

        # Create Snowpark session
        session = Session.builder.configs(connection_params).create()
        st.session_state.snowflake_session = session
        st.success("✅ Connected to Snowflake successfully!")
        return session

    except Exception as e:
        st.error(f"❌ Failed to connect to Snowflake: {str(e)}")
        st.info("💡 Using demo mode with sample data. Configure Snowflake credentials for live data.")
        return None

def get_real_data_from_snowflake(query_type: str) -> Dict:
    """Get real data from Snowflake based on query type"""
    session = st.session_state.snowflake_session
    if not session:
        return {}

    try:
        if query_type == "pipeline":
            df = session.sql("""
                SELECT stage, COUNT(*) as count, SUM(amount) as total_value, AVG(amount) as avg_value
                FROM salesgenie_ai.crm_data.opportunities
                WHERE is_won = FALSE AND is_lost = FALSE
                GROUP BY stage
                ORDER BY total_value DESC
            """).to_pandas()
            # st.markdown(df.to_markdown())

            return {"pipeline_data": df.to_dict('records')}


        elif query_type == "leads":
            df = session.sql("""
                SELECT lead_source, COUNT(*) as total_leads,
                       AVG(lead_score) as avg_score,
                       COUNT(CASE WHEN is_converted = TRUE THEN 1 END) as converted
                FROM salesgenie_ai.crm_data.leads
                GROUP BY lead_source
                ORDER BY avg_score DESC
            """).to_pandas()
            return {"lead_data": df.to_dict('records')}

        elif query_type == "performance":
            df = session.sql("""
                SELECT u.first_name || ' ' || u.last_name as sales_rep,
                       COUNT(o.opportunity_id) as total_opps,
                       COUNT(CASE WHEN o.is_won = TRUE THEN 1 END) as won_opps,
                       SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) as revenue
                FROM salesgenie_ai.crm_data.sales_users u
                LEFT JOIN salesgenie_ai.crm_data.opportunities o ON u.user_id = o.sales_rep
                WHERE u.role = 'sales_rep'
                GROUP BY u.first_name, u.last_name
                ORDER BY revenue DESC
            """).to_pandas()
            return {"performance_data": df.to_dict('records')}

        elif query_type == "analytics":
            df = session.sql("""
                SELECT territory, predicted_revenue, confidence_interval_low, confidence_interval_high
                FROM salesgenie_ai.analytics.sales_forecasts
                WHERE forecast_period = 'monthly' AND forecast_date = '2025-02-01'
                ORDER BY predicted_revenue DESC
            """).to_pandas()
            return {"analytics_data": df.to_dict('records')}

        elif query_type == "churn":
            df = session.sql("""
                SELECT c.company_name, cp.churn_probability, cp.churn_risk_level,
                       cp.key_risk_factors:factors[0]::string as top_risk_factor
                FROM salesgenie_ai.analytics.churn_predictions cp
                JOIN salesgenie_ai.crm_data.companies c ON cp.company_id = c.company_id
                ORDER BY cp.churn_probability DESC
                LIMIT 5
            """).to_pandas()
            return {"churn_data": df.to_dict('records')}

        elif query_type == "ai_usage":
            df = session.sql("""
                SELECT model_name, COUNT(*) as requests,
                       AVG(response_time_ms) as avg_response_time,
                       SUM(cost_estimate) as total_cost
                FROM salesgenie_ai.ai_services.cortex_usage_logs
                WHERE request_timestamp >= DATEADD('day', -7, CURRENT_DATE())
                GROUP BY model_name
                ORDER BY requests DESC
            """).to_pandas()
            return {"ai_usage_data": df.to_dict('records')}

        elif query_type == "integrations":
            df = session.sql("""
                SELECT es.system_name, es.system_type, es.connection_status,
                       sl.records_processed, sl.records_success, sl.sync_status
                FROM salesgenie_ai.integration.external_systems es
                LEFT JOIN salesgenie_ai.integration.sync_logs sl ON es.system_id = sl.system_id
                WHERE sl.sync_start_time = (
                    SELECT MAX(sync_start_time)
                    FROM salesgenie_ai.integration.sync_logs sl2
                    WHERE sl2.system_id = es.system_id
                )
                ORDER BY es.system_name
            """).to_pandas()
            return {"integration_data": df.to_dict('records')}

        elif query_type == "benchmarks":
            df = session.sql("""
                SELECT benchmark_type, benchmark_category, metric_name,
                       benchmark_value, percentile_50, percentile_90
                FROM salesgenie_ai.analytics.performance_benchmarks
                ORDER BY benchmark_type, metric_name
            """).to_pandas()
            return {"benchmark_data": df.to_dict('records')}

        elif query_type == "recommendations":
            df = session.sql("""
                SELECT recommendation_type, entity_type, recommendation_text,
                       confidence_score, user_feedback, action_taken
                FROM salesgenie_ai.ai_services.ai_recommendations
                ORDER BY confidence_score DESC
            """).to_pandas()
            return {"recommendation_data": df.to_dict('records')}

        elif query_type == "training_data":
            df = session.sql("""
                SELECT dataset_name, dataset_type, record_count,
                       target_column, data_quality_score
                FROM salesgenie_ai.ai_services.training_datasets
                ORDER BY data_quality_score DESC
            """).to_pandas()
            return {"training_data": df.to_dict('records')}

        elif query_type == "sync_logs":
            df = session.sql("""
                SELECT sl.sync_type, sl.sync_status, sl.records_processed,
                       sl.records_success, sl.records_failed, es.system_name
                FROM salesgenie_ai.integration.sync_logs sl
                JOIN salesgenie_ai.integration.external_systems es ON sl.system_id = es.system_id
                ORDER BY sl.sync_start_time DESC
                LIMIT 10
            """).to_pandas()
            return {"sync_log_data": df.to_dict('records')}

        elif query_type == "schema_status":
            # Get record counts from all schemas
            schemas_data = []
            schema_queries = [
                ("CRM_DATA", "companies", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.companies"),
                ("CRM_DATA", "opportunities", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.opportunities"),
                ("CRM_DATA", "leads", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.leads"),
                ("ANALYTICS", "forecasts", "SELECT COUNT(*) FROM salesgenie_ai.analytics.sales_forecasts"),
                ("ANALYTICS", "churn_predictions", "SELECT COUNT(*) FROM salesgenie_ai.analytics.churn_predictions"),
                ("AI_SERVICES", "model_configs", "SELECT COUNT(*) FROM salesgenie_ai.ai_services.ai_model_configs"),
                ("AI_SERVICES", "usage_logs", "SELECT COUNT(*) FROM salesgenie_ai.ai_services.cortex_usage_logs"),
                ("INTEGRATION", "external_systems", "SELECT COUNT(*) FROM salesgenie_ai.integration.external_systems"),
                ("INTEGRATION", "sync_logs", "SELECT COUNT(*) FROM salesgenie_ai.integration.sync_logs")
            ]

            for schema, table, query in schema_queries:
                try:
                    count_df = session.sql(query).to_pandas()
                    count = count_df.iloc[0, 0] if not count_df.empty else 0
                    schemas_data.append({
                        "schema": schema,
                        "table": table,
                        "record_count": count
                    })
                except:
                    schemas_data.append({
                        "schema": schema,
                        "table": table,
                        "record_count": 0
                    })

            return {"schema_data": schemas_data}

    except Exception as e:
        st.error(f"Error querying Snowflake: {str(e)}")
        return {}

    return {}

def get_analyst_response(messages: List[Dict]) -> Tuple[Dict, Optional[str]]:
    """
    Send chat history to the Cortex Analyst API and return the response.
 
    Args:
        messages (List[Dict]): The conversation history.

    Returns:
        Tuple[Dict, Optional[str]]: The response and any error message.
    """
    try:
        if messages and len(messages) > 0:
            user_message = messages[-1]["content"]

            # Try to get real data if Snowflake is connected
            if st.session_state.snowflake_session:
                # First try Cortex AI with real data context
                try:
                    session = st.session_state.snowflake_session

                    # Get current data context for Cortex
                    data_context = ""
                    try:
                        # Get pipeline summary
                        pipeline_result = session.sql("""
                            SELECT stage, COUNT(*) as count, SUM(amount) as total_value
                            FROM salesgenie_ai.crm_data.opportunities
                            WHERE is_won = FALSE AND is_lost = FALSE
                            GROUP BY stage
                            ORDER BY total_value DESC
                        """).collect()

                        if pipeline_result:
                            data_context += "Current Pipeline Data:\n"
                            for row in pipeline_result:
                                data_context += f"- {row[0]}: {row[1]} opportunities, ${row[2]:,.0f}\n"

                        # Get lead summary
                        lead_result = session.sql("""
                            SELECT lead_source, COUNT(*) as total, AVG(lead_score) as avg_score
                            FROM salesgenie_ai.crm_data.leads
                            GROUP BY lead_source
                            ORDER BY avg_score DESC
                        """).collect()

                        if lead_result:
                            data_context += "\nCurrent Lead Data:\n"
                            for row in lead_result:
                                data_context += f"- {row[0]}: {row[1]} leads, avg score {row[2]:.1f}\n"

                    except Exception as data_error:
                        data_context = "Unable to fetch current data context."

                    prompt = f"""You are a sales AI assistant analyzing real CRM data from Snowflake database SALESGENIE_AI.

                    User question: {user_message}

                    Current data context from the database:
                    {data_context}

                    Based on this REAL data from the SALESGENIE_AI database, provide specific insights about sales pipeline, leads, opportunities, or team performance.
                    Be specific, actionable, and professional. Use the actual numbers from the data context above.
                    Format your response with clear sections and bullet points."""

                    # Use the latest and most capable Cortex model
                    cortex_query = f"""
                    SELECT SNOWFLAKE.CORTEX.COMPLETE(
                        'llama3.1-8b',
                        '{prompt.replace("'", "''")}'
                    ) as response
                    """

                    result = session.sql(cortex_query).collect()

                    if result and len(result) > 0:
                        cortex_response = result[0]['RESPONSE']
                        return {
                            "message": {
                                "role": "analyst",
                                "content": f"🧠 **Cortex AI Analysis:**\n\n{cortex_response}"
                            },
                            "request_id": "cortex_response"
                        }, None

                except Exception as cortex_error:
                    # Cortex not available, continue with data-driven responses
                    # Only show this message once per session
                    if "cortex_warning_shown" not in st.session_state:
                        st.info("ℹ️ Snowflake Cortex not enabled in this account. Using intelligent data-driven analysis with your live Snowflake data.")
                        st.session_state.cortex_warning_shown = True

                # Use real Snowflake data for analysis
                if "pipeline" in user_message.lower():
                    real_data = get_real_data_from_snowflake("pipeline")
                    if real_data:
                        pipeline_data = real_data.get("pipeline_data", [])
                        total_value = sum(item.get("TOTAL_VALUE", 0) for item in pipeline_data)
                        total_count = sum(item.get("COUNT", 0) for item in pipeline_data)

                        content = f"Based on your current sales pipeline:\n\n**Pipeline Summary:**\n- Total Pipeline Value: ${total_value:,.0f}\n- Number of Active Opportunities: {total_count}\n\n**By Stage:**\n"
                        for item in pipeline_data:
                            stage = item.get("STAGE", "Unknown")
                            value = item.get("TOTAL_VALUE", 0)
                            count = item.get("COUNT", 0)
                            content += f"- {stage}: ${value:,.0f} ({count} opportunities)\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_pipeline"
                        }, None

                elif "lead" in user_message.lower():
                    real_data = get_real_data_from_snowflake("leads")
                    if real_data:
                        lead_data = real_data.get("lead_data", [])
                        content = "Here's your lead analysis:\n\n**Lead Performance by Source:**\n"
                        for item in lead_data:
                            source = item.get("LEAD_SOURCE", "Unknown")
                            total = item.get("TOTAL_LEADS", 0)
                            score = item.get("AVG_SCORE", 0)
                            converted = item.get("CONVERTED", 0)
                            conversion_rate = (converted / total * 100) if total > 0 else 0
                            content += f"- {source}: {total} leads (avg score: {score:.1f}, conversion: {conversion_rate:.1f}%)\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_leads"
                        }, None

                elif "performance" in user_message.lower() or "rep" in user_message.lower():
                    real_data = get_real_data_from_snowflake("performance")
                    if real_data:
                        perf_data = real_data.get("performance_data", [])
                        content = "**Sales Rep Performance Analysis:**\n\n"
                        for item in perf_data:
                            rep = item.get("SALES_REP", "Unknown")
                            total_opps = item.get("TOTAL_OPPS", 0)
                            won_opps = item.get("WON_OPPS", 0)
                            revenue = item.get("REVENUE", 0)
                            win_rate = (won_opps / total_opps * 100) if total_opps > 0 else 0
                            content += f"**{rep}:**\n- Opportunities: {total_opps}\n- Won: {won_opps}\n- Revenue: ${revenue:,.0f}\n- Win Rate: {win_rate:.1f}%\n\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_performance"
                        }, None

                elif "forecast" in user_message.lower() or "analytics" in user_message.lower():
                    real_data = get_real_data_from_snowflake("analytics")
                    if real_data:
                        analytics_data = real_data.get("analytics_data", [])
                        content = "**Sales Forecasting Analysis:**\n\n"
                        total_forecast = sum(item.get("PREDICTED_REVENUE", 0) for item in analytics_data)
                        content += f"**Total Monthly Forecast:** ${total_forecast:,.0f}\n\n**By Territory:**\n"
                        for item in analytics_data:
                            territory = item.get("TERRITORY", "Unknown")
                            forecast = item.get("PREDICTED_REVENUE", 0)
                            low = item.get("CONFIDENCE_INTERVAL_LOW", 0)
                            high = item.get("CONFIDENCE_INTERVAL_HIGH", 0)
                            content += f"- {territory}: ${forecast:,.0f} (Range: ${low:,.0f} - ${high:,.0f})\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_analytics"
                        }, None

                elif "churn" in user_message.lower() or "risk" in user_message.lower():
                    real_data = get_real_data_from_snowflake("churn")
                    if real_data:
                        churn_data = real_data.get("churn_data", [])
                        content = "**Customer Churn Risk Analysis:**\n\n"
                        for item in churn_data:
                            company = item.get("COMPANY_NAME", "Unknown")
                            probability = item.get("CHURN_PROBABILITY", 0)
                            risk_level = item.get("CHURN_RISK_LEVEL", "Unknown")
                            risk_factor = item.get("TOP_RISK_FACTOR", "Unknown")
                            content += f"**{company}:**\n- Churn Probability: {probability*100:.1f}%\n- Risk Level: {risk_level}\n- Key Risk: {risk_factor}\n\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_churn"
                        }, None

                elif "ai" in user_message.lower() or "model" in user_message.lower() or "usage" in user_message.lower():
                    real_data = get_real_data_from_snowflake("ai_usage")
                    if real_data:
                        ai_data = real_data.get("ai_usage_data", [])
                        content = "**AI Services Usage Analysis (Last 7 Days):**\n\n"
                        total_requests = sum(item.get("REQUESTS", 0) for item in ai_data)
                        total_cost = sum(item.get("TOTAL_COST", 0) for item in ai_data)
                        content += f"**Total Requests:** {total_requests:,}\n**Total Cost:** ${total_cost:.4f}\n\n**By Model:**\n"
                        for item in ai_data:
                            model = item.get("MODEL_NAME", "Unknown")
                            requests = item.get("REQUESTS", 0)
                            avg_time = item.get("AVG_RESPONSE_TIME", 0)
                            cost = item.get("TOTAL_COST", 0)
                            content += f"- {model}: {requests:,} requests, {avg_time:.0f}ms avg, ${cost:.4f}\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_ai_usage"
                        }, None

                elif "integration" in user_message.lower() or "sync" in user_message.lower():
                    real_data = get_real_data_from_snowflake("integrations")
                    if real_data:
                        integration_data = real_data.get("integration_data", [])
                        content = "**External System Integration Status:**\n\n"
                        for item in integration_data:
                            system = item.get("SYSTEM_NAME", "Unknown")
                            system_type = item.get("SYSTEM_TYPE", "Unknown")
                            status = item.get("CONNECTION_STATUS", "Unknown")
                            processed = item.get("RECORDS_PROCESSED", 0)
                            success = item.get("RECORDS_SUCCESS", 0)
                            sync_status = item.get("SYNC_STATUS", "Unknown")
                            success_rate = (success / processed * 100) if processed > 0 else 0
                            content += f"**{system} ({system_type}):**\n- Status: {status}\n- Last Sync: {sync_status}\n- Records: {processed:,} processed, {success_rate:.1f}% success\n\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_integrations"
                        }, None

                elif "benchmark" in user_message.lower() or "metric" in user_message.lower():
                    real_data = get_real_data_from_snowflake("benchmarks")
                    if real_data:
                        benchmark_data = real_data.get("benchmark_data", [])
                        content = "**Performance Benchmarks Analysis:**\n\n"
                        for item in benchmark_data:
                            bench_type = item.get("BENCHMARK_TYPE", "Unknown")
                            category = item.get("BENCHMARK_CATEGORY", "Unknown")
                            metric = item.get("METRIC_NAME", "Unknown")
                            value = item.get("BENCHMARK_VALUE", 0)
                            p50 = item.get("PERCENTILE_50", 0)
                            p90 = item.get("PERCENTILE_90", 0)
                            content += f"**{metric} ({category}):**\n- Benchmark: {value:.3f}\n- 50th Percentile: {p50:.3f}\n- 90th Percentile: {p90:.3f}\n- Type: {bench_type}\n\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_benchmarks"
                        }, None

                elif "recommendation" in user_message.lower() or "insight" in user_message.lower():
                    real_data = get_real_data_from_snowflake("recommendations")
                    if real_data:
                        rec_data = real_data.get("recommendation_data", [])
                        content = "**AI Recommendations & Insights:**\n\n"
                        for item in rec_data:
                            rec_type = item.get("RECOMMENDATION_TYPE", "Unknown")
                            entity_type = item.get("ENTITY_TYPE", "Unknown")
                            text = item.get("RECOMMENDATION_TEXT", "No recommendation")
                            confidence = item.get("CONFIDENCE_SCORE", 0)
                            feedback = item.get("USER_FEEDBACK", "No feedback")
                            action_taken = item.get("ACTION_TAKEN", False)
                            content += f"**{rec_type.title()} ({entity_type}):**\n- {text}\n- Confidence: {confidence*100:.1f}%\n- Feedback: {feedback}\n- Action Taken: {'Yes' if action_taken else 'No'}\n\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_recommendations"
                        }, None

                elif "training" in user_message.lower() or "dataset" in user_message.lower():
                    real_data = get_real_data_from_snowflake("training_data")
                    if real_data:
                        training_data = real_data.get("training_data", [])
                        content = "**Training Datasets & Model Performance:**\n\n"
                        for item in training_data:
                            name = item.get("DATASET_NAME", "Unknown")
                            dataset_type = item.get("DATASET_TYPE", "Unknown")
                            count = item.get("RECORD_COUNT", 0)
                            target = item.get("TARGET_COLUMN", "Unknown")
                            quality = item.get("DATA_QUALITY_SCORE", 0)
                            content += f"**{name}:**\n- Type: {dataset_type}\n- Records: {count:,}\n- Target: {target}\n- Quality Score: {quality*100:.1f}%\n\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_training"
                        }, None

                elif "schema" in user_message.lower() or "database" in user_message.lower():
                    real_data = get_real_data_from_snowflake("schema_status")
                    if real_data:
                        schema_data = real_data.get("schema_data", [])
                        content = "**Database Schema Status:**\n\n"

                        # Group by schema
                        schemas = {}
                        for item in schema_data:
                            schema = item.get("schema", "Unknown")
                            if schema not in schemas:
                                schemas[schema] = []
                            schemas[schema].append(item)

                        for schema, tables in schemas.items():
                            total_records = sum(table.get("record_count", 0) for table in tables)
                            content += f"**{schema} Schema:**\n"
                            for table in tables:
                                table_name = table.get("table", "Unknown")
                                count = table.get("record_count", 0)
                                content += f"- {table_name}: {count:,} records\n"
                            content += f"- **Total: {total_records:,} records**\n\n"

                        return {
                            "message": {
                                "role": "analyst",
                                "content": content
                            },
                            "request_id": "real_data_schema"
                        }, None

            # Fallback to demo responses if no Snowflake connection
            if "pipeline" in user_message.lower():
                response = {
                    "message": {
                        "role": "analyst",
                        "content": "Based on your current sales pipeline, here's what I found:\n\n**Pipeline Summary:**\n- Total Pipeline Value: $535,000\n- Number of Active Opportunities: 6\n- Average Deal Size: $89,167\n\n**By Stage:**\n- Discovery: $165,000 (2 opportunities)\n- Qualification: $150,000 (1 opportunity) \n- Proposal: $160,000 (2 opportunities)\n- Negotiation: $95,000 (2 opportunities)\n\n**Key Insights:**\n- 75% of pipeline value is in early stages (Discovery/Qualification)\n- Negotiation stage deals have highest close probability\n- Recommend focusing on moving Proposal stage deals forward"
                    },
                    "request_id": "demo_request_123"
                }
            elif "lead" in user_message.lower():
                response = {
                    "message": {
                        "role": "analyst",
                        "content": "Here's your lead analysis:\n\n**Lead Performance:**\n- Total Active Leads: 6\n- Average Lead Score: 69.2\n- Conversion Rate: 16.7%\n\n**By Source:**\n- Website: 1 lead (85.5 score) - Hot\n- Trade Show: 1 lead (65.0 score) - Warm\n- Referral: 1 lead (90.2 score) - Hot\n- Cold Call: 1 lead (45.0 score) - Cold\n- LinkedIn: 1 lead (75.8 score) - Warm\n- Email Campaign: 1 lead (55.0 score) - Warm\n\n**Recommendations:**\n- Prioritize referral and website leads (highest scores)\n- Follow up on overdue LinkedIn lead\n- Nurture cold call lead with targeted content"
                    },
                    "request_id": "demo_request_124"
                }
            elif "performance" in user_message.lower() or "rep" in user_message.lower():
                response = {
                    "message": {
                        "role": "analyst",
                        "content": "**Sales Rep Performance Analysis:**\n\n**Sarah Johnson (West Coast):**\n- Opportunities: 3\n- Pipeline Value: $170,000\n- Win Rate: 33.3%\n- Revenue: $95,000\n\n**Mike Davis (East Coast):**\n- Opportunities: 3  \n- Pipeline Value: $225,000\n- Win Rate: 0%\n- Revenue: $0\n\n**Lisa Wilson (Central):**\n- Opportunities: 2\n- Pipeline Value: $170,000\n- Win Rate: 0%\n- Revenue: $0\n\n**Key Insights:**\n- Sarah leads in closed revenue\n- Mike has highest pipeline value but needs closing support\n- Lisa needs lead generation assistance\n- Overall team win rate: 12.5%"
                    },
                    "request_id": "demo_request_125"
                }
            else:
                response = {
                    "message": {
                        "role": "analyst",
                        "content": f"I can help you analyze your sales data! Here are some things you can ask me:\n\n**Pipeline Analysis:**\n- 'Show me the pipeline by stage'\n- 'What are my top opportunities?'\n- 'Which deals are at risk?'\n\n**Lead Management:**\n- 'How are my leads performing?'\n- 'Which leads need follow-up?'\n- 'What's my conversion rate by source?'\n\n**Sales Performance:**\n- 'Show me sales rep performance'\n- 'What's our team win rate?'\n- 'Who needs coaching support?'\n\n**Forecasting:**\n- 'What's our quarterly forecast?'\n- 'Which deals will close this month?'\n- 'Show me revenue trends'\n\nWhat would you like to explore?"
                    },
                    "request_id": "demo_request_126"
                }

            return response, None

    except Exception as e:
        error_msg = f"Error calling Cortex Analyst: {str(e)}"
        return {}, error_msg

def create_crm_action(action_type: str, details: Dict) -> str:
    """
    Process CRM actions like creating leads, updating opportunities, etc.

    Args:
        action_type (str): Type of CRM action
        details (Dict): Action details

    Returns:
        str: Success message
    """
    if action_type == "create_lead":
        return f"✅ Created new lead for {details.get('company', 'Unknown Company')}"
    elif action_type == "update_opportunity":
        return f"✅ Updated opportunity {details.get('name', 'Unknown')} to {details.get('stage', 'Unknown')} stage"
    elif action_type == "schedule_follow_up":
        return f"✅ Scheduled follow-up with {details.get('contact', 'Unknown')} for {details.get('date', 'Unknown')}"
    else:
        return "✅ CRM action completed successfully"

def display_sales_metrics():
    """Display key sales metrics in the sidebar"""
    st.sidebar.markdown("### 📊 Key Metrics")

    # Sample metrics - in real app, these would come from Snowflake
    col1, col2 = st.sidebar.columns(2)

    with col1:
        st.metric("Pipeline Value", "$535K", "+12%")
        st.metric("Active Leads", "6", "+2")

    with col2:
        st.metric("Win Rate", "12.5%", "-2.1%")
        st.metric("Avg Deal Size", "$89K", "+5%")

def display_quick_actions():
    """Display quick action buttons organized by schema"""
    st.sidebar.markdown("### ⚡ Quick Actions")

    # Core CRM Actions (CRM_DATA Schema)
    st.sidebar.markdown("**📊 Core CRM**")
    col1, col2 = st.sidebar.columns(2)

    with col1:
        if st.button("📈 Pipeline", key="pipeline_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me my current sales pipeline by stage"
            })
            st.rerun()

        if st.button("👥 Team", key="team_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me sales rep performance metrics"
            })
            st.rerun()

    with col2:
        if st.button("🎯 Leads", key="leads_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Analyze my lead performance and conversion rates"
            })
            st.rerun()

        if st.button("📅 Follow-up", key="followup_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Which leads and opportunities need follow-up?"
            })
            st.rerun()

    # Advanced Analytics (ANALYTICS Schema)
    st.sidebar.markdown("**📈 Advanced Analytics**")
    col3, col4 = st.sidebar.columns(2)

    with col3:
        if st.button("📊 Forecast", key="forecast_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me the sales forecast and analytics"
            })
            st.rerun()

        if st.button("📋 Benchmarks", key="benchmarks_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me performance benchmarks and metrics"
            })
            st.rerun()

    with col4:
        if st.button("⚠️ Churn Risk", key="churn_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Analyze customer churn risk"
            })
            st.rerun()

        if st.button("📊 Health Score", key="health_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me customer health scores"
            })
            st.rerun()

    # AI Services (AI_SERVICES Schema)
    st.sidebar.markdown("**🤖 AI Services**")
    col5, col6 = st.sidebar.columns(2)

    with col5:
        if st.button("🤖 AI Usage", key="ai_usage_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me AI model usage and performance"
            })
            st.rerun()

        if st.button("🎯 Recommendations", key="recommendations_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me AI recommendations and insights"
            })
            st.rerun()

    with col6:
        if st.button("📚 Training Data", key="training_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me training datasets and model performance"
            })
            st.rerun()

        if st.button("💬 Conversations", key="conversations_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me conversation history and satisfaction"
            })
            st.rerun()

    # Integration Services (INTEGRATION Schema)
    st.sidebar.markdown("**🔗 Integration**")
    col7, col8 = st.sidebar.columns(2)

    with col7:
        if st.button("🔗 Systems", key="systems_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Check external system integration status"
            })
            st.rerun()

        if st.button("📋 Staging", key="staging_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me staging data and sync status"
            })
            st.rerun()

    with col8:
        if st.button("📊 Sync Logs", key="sync_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me data synchronization logs"
            })
            st.rerun()

        if st.button("🗄️ Schema Status", key="schema_sidebar"):
            st.session_state.messages.append({
                "role": "user",
                "content": "Show me complete database schema status"
            })
            st.rerun()

def main():
    """Main application function"""

    # Initialize Snowflake connection if not already done
    if st.session_state.snowflake_session is None and SNOWFLAKE_AVAILABLE:
        with st.spinner("Connecting to Snowflake..."):
            init_snowflake_connection()

    # Header
    st.markdown('<h1 class="main-header">🚀 SalesGenie AI</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #666;">Conversational AI for Sales Excellence</p>', unsafe_allow_html=True)

    # Connection status indicator
    if st.session_state.snowflake_session:
        st.success("🔗 Connected to Snowflake - Using live data")
    else:
        st.info("📊 Demo mode - Using sample data")

    # Sidebar
    with st.sidebar:
        st.markdown("### 🎯 SalesGenie AI")
        st.markdown("Your intelligent sales assistant powered by Snowflake Cortex")

        display_sales_metrics()
        display_quick_actions()

        st.markdown("---")
        st.markdown("### 🔧 Settings")

        # Model selection
        model_option = st.selectbox(
            "AI Model",
            ["claude-3-5-sonnet", "llama3.1-70b", "mistral-large2"],
            help="Choose the AI model for responses"
        )

        # Voice input toggle
        voice_enabled = st.checkbox("🎤 Voice Input", help="Enable voice-to-text input")

        if st.button("🗑️ Clear Chat"):
            st.session_state.messages = []
            st.rerun()

    # Main chat interface
    st.markdown("### 💬 Chat with your Sales Data")

    # Display chat messages
    for message in st.session_state.messages:
        if message["role"] == "user":
            st.markdown(f'<div class="chat-message user-message"><strong>You:</strong> {message["content"]}</div>', unsafe_allow_html=True)
        else:
            st.markdown(f'<div class="chat-message ai-message"><strong>SalesGenie AI:</strong> {message["content"]}</div>', unsafe_allow_html=True)

    # Chat input
    user_input = st.chat_input("Ask me about your sales data, pipeline, leads, or team performance...")

    if user_input:
        # Add user message
        st.session_state.messages.append({"role": "user", "content": user_input})

        # Get AI response
        with st.spinner("Analyzing your sales data..."):
            response, error = get_analyst_response(st.session_state.messages)

            if error:
                st.error(error)
            else:
                ai_response = response.get("message", {}).get("content", "I'm sorry, I couldn't process that request.")
                st.session_state.messages.append({"role": "assistant", "content": ai_response})

        st.rerun()

    # Sample questions
    if not st.session_state.messages:
        st.markdown("### 💡 Try asking me about your comprehensive sales data:")

        # Core CRM Questions
        st.markdown("**📊 Core CRM Analytics**")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("📊 Pipeline Analysis", key="sample_pipeline"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Show me my sales pipeline breakdown by stage"
                })
                st.rerun()

        with col2:
            if st.button("🎯 Lead Insights", key="sample_leads"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Analyze my lead performance and scoring"
                })
                st.rerun()

        with col3:
            if st.button("👥 Team Metrics", key="sample_team"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Show me sales team performance"
                })
                st.rerun()

        with col4:
            if st.button("� Follow-ups", key="sample_followup"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Which leads and opportunities need follow-up?"
                })
                st.rerun()

        # Advanced Analytics Questions
        st.markdown("**�📈 Advanced Analytics**")
        col5, col6, col7, col8 = st.columns(4)

        with col5:
            if st.button("📈 Sales Forecast", key="sample_forecast"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Show me the sales forecast and analytics"
                })
                st.rerun()

        with col6:
            if st.button("⚠️ Churn Analysis", key="sample_churn"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Analyze customer churn risk"
                })
                st.rerun()

        with col7:
            if st.button("📋 Benchmarks", key="sample_benchmarks"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Show me performance benchmarks and metrics"
                })
                st.rerun()

        with col8:
            if st.button("📊 Health Scores", key="sample_health"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Show me customer health scores"
                })
                st.rerun()

        # AI Services Questions
        st.markdown("**🤖 AI Services & Integration**")
        col9, col10, col11, col12 = st.columns(4)

        with col9:
            if st.button("🤖 AI Usage", key="sample_ai"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Show me AI model usage and performance"
                })
                st.rerun()

        with col10:
            if st.button("🎯 AI Recommendations", key="sample_recommendations"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Show me AI recommendations and insights"
                })
                st.rerun()

        with col11:
            if st.button("🔗 Integrations", key="sample_integrations"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Check external system integration status"
                })
                st.rerun()

        with col12:
            if st.button("🗄️ Schema Status", key="sample_schema"):
                st.session_state.messages.append({
                    "role": "user",
                    "content": "Show me complete database schema status"
                })
                st.rerun()

        # Highlight comprehensive capabilities
        st.markdown("---")
        st.markdown("### 🎉 **Comprehensive 4-Schema Architecture**")
        st.markdown("""
        **SalesGenie AI now has complete data across all schemas:**
        - 📊 **CRM_DATA**: Companies, leads, opportunities, activities, sales users
        - 📈 **ANALYTICS**: Forecasts, churn predictions, benchmarks, historical metrics
        - 🤖 **AI_SERVICES**: Model configs, usage logs, training datasets, recommendations
        - 🔗 **INTEGRATION**: External systems, sync logs, staging data, webhooks

        **Try asking complex questions like:**
        - "Compare my team's performance against industry benchmarks"
        - "Which AI models are performing best and what's the cost?"
        - "Show me integration status and any sync issues"
        - "What are the AI recommendations for my high-risk customers?"
        """)

        st.markdown("---")

if __name__ == "__main__":
    main()
