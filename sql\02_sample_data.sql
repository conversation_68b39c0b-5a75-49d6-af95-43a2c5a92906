/*
SalesGenie AI - Sample Data Generation
Nihilent x Snowflake Hackathon 2025

This script populates the database with realistic sample data for demonstration
*/

USE ROLE salesgenie_ai_role;
USE WAREHOUSE salesgenie_ai_wh;
USE DATABASE salesgenie_ai;
USE SCHEMA salesgenie_ai.crm_data;

-- =====================================================
-- SAMPLE COMPANIES DATA
-- =====================================================

INSERT INTO companies (company_id, company_name, industry, company_size, annual_revenue, website, phone, address, city, state, country, postal_code, created_by) VALUES
('comp_001', 'TechCorp Solutions', 'Technology', 'Enterprise (1000+)', 50000000.00, 'www.techcorp.com', '******-0101', '123 Tech Street', 'San Francisco', 'CA', 'USA', '94105', 'user_002'),
('comp_002', 'Global Manufacturing Inc', 'Manufacturing', 'Large (500-999)', 25000000.00, 'www.globalmanuf.com', '******-0102', '456 Industrial Ave', 'Detroit', 'MI', 'USA', '48201', 'user_003'),
('comp_003', 'HealthTech Innovations', 'Healthcare', 'Medium (100-499)', 15000000.00, 'www.healthtech.com', '******-0103', '789 Medical Plaza', 'Boston', 'MA', 'USA', '02101', 'user_003'),
('comp_004', 'Financial Services Group', 'Financial Services', 'Large (500-999)', 35000000.00, 'www.finservices.com', '******-0104', '321 Wall Street', 'New York', 'NY', 'USA', '10005', 'user_003'),
('comp_005', 'Retail Chain Corp', 'Retail', 'Enterprise (1000+)', 75000000.00, 'www.retailchain.com', '******-0105', '654 Commerce Blvd', 'Chicago', 'IL', 'USA', '60601', 'user_004'),
('comp_006', 'Education Solutions Ltd', 'Education', 'Medium (100-499)', 8000000.00, 'www.edusolutions.com', '******-0106', '987 Campus Drive', 'Austin', 'TX', 'USA', '73301', 'user_002'),
('comp_007', 'Energy Systems Inc', 'Energy', 'Large (500-999)', 45000000.00, 'www.energysys.com', '******-0107', '147 Power Lane', 'Houston', 'TX', 'USA', '77001', 'user_004'),
('comp_008', 'Logistics Partners', 'Transportation', 'Medium (100-499)', 12000000.00, 'www.logisticspart.com', '******-0108', '258 Freight Way', 'Atlanta', 'GA', 'USA', '30301', 'user_002'),
('comp_009', 'Media & Entertainment Co', 'Media', 'Medium (100-499)', 18000000.00, 'www.mediaent.com', '******-0109', '369 Studio Blvd', 'Los Angeles', 'CA', 'USA', '90210', 'user_002'),
('comp_010', 'Consulting Experts LLC', 'Consulting', 'Small (50-99)', 5000000.00, 'www.consultexp.com', '******-0110', '741 Advisory Street', 'Seattle', 'WA', 'USA', '98101', 'user_004');

-- =====================================================
-- SAMPLE CONTACTS DATA
-- =====================================================

INSERT INTO contacts (contact_id, company_id, first_name, last_name, email, phone, mobile_phone, job_title, department, decision_maker, linkedin_url, created_by) VALUES
('cont_001', 'comp_001', 'Alice', 'Johnson', '<EMAIL>', '******-0201', '******-0301', 'CTO', 'Technology', TRUE, 'linkedin.com/in/alicejohnson', 'user_002'),
('cont_002', 'comp_001', 'Bob', 'Smith', '<EMAIL>', '******-0202', '******-0302', 'VP Sales', 'Sales', TRUE, 'linkedin.com/in/bobsmith', 'user_002'),
('cont_003', 'comp_002', 'Carol', 'Davis', '<EMAIL>', '******-0203', '******-0303', 'Operations Director', 'Operations', TRUE, 'linkedin.com/in/caroldavis', 'user_003'),
('cont_004', 'comp_002', 'David', 'Wilson', '<EMAIL>', '******-0204', '******-0304', 'IT Manager', 'IT', FALSE, 'linkedin.com/in/davidwilson', 'user_003'),
('cont_005', 'comp_003', 'Emma', 'Brown', '<EMAIL>', '******-0205', '******-0305', 'Chief Medical Officer', 'Medical', TRUE, 'linkedin.com/in/emmabrown', 'user_003'),
('cont_006', 'comp_003', 'Frank', 'Miller', '<EMAIL>', '******-0206', '******-0306', 'Head of IT', 'Technology', TRUE, 'linkedin.com/in/frankmiller', 'user_003'),
('cont_007', 'comp_004', 'Grace', 'Taylor', '<EMAIL>', '******-0207', '******-0307', 'CFO', 'Finance', TRUE, 'linkedin.com/in/gracetaylor', 'user_003'),
('cont_008', 'comp_004', 'Henry', 'Anderson', '<EMAIL>', '******-0208', '******-0308', 'VP Technology', 'Technology', TRUE, 'linkedin.com/in/henryanderson', 'user_003'),
('cont_009', 'comp_005', 'Ivy', 'Thomas', '<EMAIL>', '******-0209', '******-0309', 'Chief Digital Officer', 'Digital', TRUE, 'linkedin.com/in/ivythomas', 'user_004'),
('cont_010', 'comp_005', 'Jack', 'Jackson', '<EMAIL>', '******-0210', '******-0310', 'VP Operations', 'Operations', TRUE, 'linkedin.com/in/jackjackson', 'user_004'),
('cont_011', 'comp_006', 'Kate', 'White', '<EMAIL>', '******-0211', '******-0311', 'Dean of Technology', 'Technology', TRUE, 'linkedin.com/in/katewhite', 'user_002'),
('cont_012', 'comp_007', 'Liam', 'Harris', '<EMAIL>', '******-0212', '******-0312', 'Chief Engineer', 'Engineering', TRUE, 'linkedin.com/in/liamharris', 'user_004'),
('cont_013', 'comp_008', 'Mia', 'Martin', '<EMAIL>', '******-0213', '******-0313', 'VP Logistics', 'Operations', TRUE, 'linkedin.com/in/miamartin', 'user_002'),
('cont_014', 'comp_009', 'Noah', 'Garcia', '<EMAIL>', '******-0214', '******-0314', 'Head of Technology', 'Technology', TRUE, 'linkedin.com/in/noahgarcia', 'user_002'),
('cont_015', 'comp_010', 'Olivia', 'Rodriguez', '<EMAIL>', '******-0215', '******-0315', 'Managing Partner', 'Executive', TRUE, 'linkedin.com/in/oliviarodriguez', 'user_004');

-- =====================================================
-- SAMPLE LEADS DATA
-- =====================================================

INSERT INTO leads (lead_id, company_id, contact_id, lead_source, lead_status, lead_score, qualification_level, estimated_value, estimated_close_date, assigned_to, last_contacted_date, next_follow_up_date, notes) VALUES
('lead_001', 'comp_001', 'cont_001', 'Website', 'Qualified', 85.5, 'Hot', 75000.00, '2025-03-15', 'user_002', '2025-01-10 14:30:00', '2025-01-15 10:00:00', 'Very interested in enterprise CRM solution. Budget approved.'),
('lead_002', 'comp_002', 'cont_003', 'Trade Show', 'New', 65.0, 'Warm', 45000.00, '2025-04-20', 'user_003', '2025-01-08 16:45:00', '2025-01-12 09:00:00', 'Met at manufacturing expo. Needs integration with existing ERP.'),
('lead_003', 'comp_003', 'cont_005', 'Referral', 'Qualified', 90.2, 'Hot', 60000.00, '2025-02-28', 'user_003', '2025-01-11 11:15:00', '2025-01-16 14:00:00', 'Referred by existing customer. Ready to move forward quickly.'),
('lead_004', 'comp_006', 'cont_011', 'Cold Call', 'Contacted', 45.0, 'Cold', 25000.00, '2025-05-30', 'user_002', '2025-01-09 13:20:00', '2025-01-14 15:30:00', 'Initial interest shown. Need to understand budget timeline.'),
('lead_005', 'comp_008', 'cont_013', 'LinkedIn', 'Qualified', 75.8, 'Warm', 35000.00, '2025-03-31', 'user_002', '2025-01-10 10:45:00', '2025-01-17 11:00:00', 'Connected via LinkedIn. Looking for logistics optimization tools.'),
('lead_006', 'comp_010', 'cont_015', 'Email Campaign', 'New', 55.0, 'Warm', 20000.00, '2025-06-15', 'user_004', '2025-01-07 09:30:00', '2025-01-13 16:00:00', 'Responded to email campaign. Small but growing consulting firm.');

-- =====================================================
-- SAMPLE OPPORTUNITIES DATA
-- =====================================================

INSERT INTO opportunities (opportunity_id, company_id, contact_id, lead_id, opportunity_name, stage, probability, amount, expected_close_date, sales_rep, sales_manager, product_category, created_by) VALUES
('opp_001', 'comp_001', 'cont_001', 'lead_001', 'TechCorp CRM Implementation', 'Proposal', 75.0, 75000.00, '2025-03-15', 'user_002', 'user_001', 'Software', 'user_002'),
('opp_002', 'comp_003', 'cont_005', 'lead_003', 'HealthTech Analytics Platform', 'Negotiation', 85.0, 60000.00, '2025-02-28', 'user_003', 'user_001', 'Software', 'user_003'),
('opp_003', 'comp_004', 'cont_007', NULL, 'Financial Services Integration', 'Discovery', 40.0, 120000.00, '2025-05-15', 'user_003', 'user_001', 'Software', 'user_003'),
('opp_004', 'comp_005', 'cont_009', NULL, 'Retail Chain Digital Transformation', 'Qualification', 60.0, 150000.00, '2025-04-30', 'user_004', 'user_001', 'Software', 'user_004'),
('opp_005', 'comp_007', 'cont_012', NULL, 'Energy Systems Mobile Solution', 'Proposal', 70.0, 85000.00, '2025-03-20', 'user_004', 'user_001', 'Software', 'user_004'),
('opp_006', 'comp_002', 'cont_003', 'lead_002', 'Manufacturing ERP Integration', 'Discovery', 50.0, 45000.00, '2025-04-20', 'user_003', 'user_001', 'Services', 'user_003'),
('opp_007', 'comp_009', 'cont_014', NULL, 'Media Company Analytics Suite', 'Closed Won', 100.0, 95000.00, '2025-01-05', 'user_002', 'user_001', 'Software', 'user_002'),
('opp_008', 'comp_008', 'cont_013', 'lead_005', 'Logistics Optimization Platform', 'Negotiation', 80.0, 35000.00, '2025-03-31', 'user_002', 'user_001', 'Software', 'user_002');

-- Update won opportunity
UPDATE opportunities SET is_won = TRUE, actual_close_date = '2025-01-05' WHERE opportunity_id = 'opp_007';

-- =====================================================
-- SAMPLE OPPORTUNITY PRODUCTS DATA
-- =====================================================

INSERT INTO opportunity_products (opportunity_id, product_id, quantity, unit_price, discount_percent, total_amount) VALUES
('opp_001', 'prod_001', 1, 50000.00, 10.0, 45000.00),
('opp_001', 'prod_004', 1, 10000.00, 0.0, 10000.00),
('opp_001', 'prod_005', 1, 5000.00, 0.0, 5000.00),
('opp_002', 'prod_002', 1, 25000.00, 5.0, 23750.00),
('opp_002', 'prod_004', 1, 10000.00, 0.0, 10000.00),
('opp_003', 'prod_001', 1, 50000.00, 0.0, 50000.00),
('opp_003', 'prod_002', 1, 25000.00, 0.0, 25000.00),
('opp_003', 'prod_004', 2, 10000.00, 15.0, 17000.00),
('opp_004', 'prod_001', 1, 50000.00, 0.0, 50000.00),
('opp_004', 'prod_002', 1, 25000.00, 0.0, 25000.00),
('opp_004', 'prod_003', 1, 15000.00, 0.0, 15000.00),
('opp_004', 'prod_004', 3, 10000.00, 10.0, 27000.00),
('opp_005', 'prod_003', 1, 15000.00, 0.0, 15000.00),
('opp_005', 'prod_004', 2, 10000.00, 5.0, 19000.00),
('opp_006', 'prod_004', 3, 10000.00, 0.0, 30000.00),
('opp_006', 'prod_005', 1, 5000.00, 0.0, 5000.00),
('opp_007', 'prod_002', 1, 25000.00, 0.0, 25000.00),
('opp_007', 'prod_003', 1, 15000.00, 0.0, 15000.00),
('opp_007', 'prod_004', 2, 10000.00, 0.0, 20000.00),
('opp_008', 'prod_002', 1, 25000.00, 5.0, 23750.00),
('opp_008', 'prod_005', 1, 5000.00, 0.0, 5000.00);

-- =====================================================
-- SAMPLE ACTIVITIES DATA
-- =====================================================

INSERT INTO activities (activity_id, activity_type, subject, description, activity_date, duration_minutes, company_id, contact_id, opportunity_id, lead_id, assigned_to, completed, created_by) VALUES
('act_001', 'call', 'Initial Discovery Call', 'Discussed CRM requirements and current pain points', '2025-01-08 14:00:00', 45, 'comp_001', 'cont_001', 'opp_001', 'lead_001', 'user_002', TRUE, 'user_002'),
('act_002', 'email', 'Proposal Follow-up', 'Sent detailed proposal with pricing options', '2025-01-09 10:30:00', NULL, 'comp_001', 'cont_001', 'opp_001', 'lead_001', 'user_002', TRUE, 'user_002'),
('act_003', 'meeting', 'Product Demo', 'Demonstrated CRM features and integration capabilities', '2025-01-10 15:00:00', 90, 'comp_001', 'cont_001', 'opp_001', 'lead_001', 'user_002', TRUE, 'user_002'),
('act_004', 'call', 'Budget Discussion', 'Confirmed budget and decision timeline', '2025-01-11 11:00:00', 30, 'comp_003', 'cont_005', 'opp_002', 'lead_003', 'user_003', TRUE, 'user_003'),
('act_005', 'meeting', 'Stakeholder Meeting', 'Met with IT team to discuss technical requirements', '2025-01-09 14:30:00', 60, 'comp_003', 'cont_006', 'opp_002', 'lead_003', 'user_003', TRUE, 'user_003'),
('act_006', 'task', 'Prepare Custom Demo', 'Create customized demo for healthcare industry', '2025-01-12 09:00:00', NULL, 'comp_003', 'cont_005', 'opp_002', 'lead_003', 'user_003', FALSE, 'user_003'),
('act_007', 'call', 'Cold Outreach', 'Initial contact to introduce our solutions', '2025-01-07 16:00:00', 20, 'comp_004', 'cont_007', NULL, NULL, 'user_003', TRUE, 'user_003'),
('act_008', 'email', 'Information Request', 'Sent company overview and case studies', '2025-01-08 09:15:00', NULL, 'comp_004', 'cont_007', 'opp_003', NULL, 'user_003', TRUE, 'user_003'),
('act_009', 'meeting', 'Needs Assessment', 'Comprehensive review of current systems and requirements', '2025-01-10 13:00:00', 120, 'comp_005', 'cont_009', 'opp_004', NULL, 'user_004', TRUE, 'user_004'),
('act_010', 'call', 'Technical Discussion', 'Discussed integration requirements with IT team', '2025-01-11 10:30:00', 45, 'comp_005', 'cont_010', 'opp_004', NULL, 'user_004', TRUE, 'user_004'),
('act_011', 'task', 'Proposal Preparation', 'Prepare comprehensive proposal for retail solution', '2025-01-13 14:00:00', NULL, 'comp_005', 'cont_009', 'opp_004', NULL, 'user_004', FALSE, 'user_004'),
('act_012', 'email', 'Contract Negotiation', 'Discussed contract terms and implementation timeline', '2025-01-04 11:00:00', NULL, 'comp_009', 'cont_014', 'opp_007', NULL, 'user_002', TRUE, 'user_002'),
('act_013', 'meeting', 'Contract Signing', 'Finalized agreement and signed contract', '2025-01-05 14:00:00', 60, 'comp_009', 'cont_014', 'opp_007', NULL, 'user_002', TRUE, 'user_002'),
('act_014', 'call', 'Follow-up Call', 'Checked on logistics optimization needs', '2025-01-10 16:00:00', 25, 'comp_008', 'cont_013', 'opp_008', 'lead_005', 'user_002', TRUE, 'user_002'),
('act_015', 'task', 'Research Competitor', 'Research competing solutions in logistics space', '2025-01-14 10:00:00', NULL, 'comp_008', 'cont_013', 'opp_008', 'lead_005', 'user_002', FALSE, 'user_002');

COMMIT;

-- =====================================================
-- CREATE VIEWS FOR ANALYTICS
-- =====================================================

USE SCHEMA salesgenie_ai.analytics;

-- Sales pipeline view
CREATE OR REPLACE VIEW sales_pipeline AS
SELECT 
    o.opportunity_id,
    o.opportunity_name,
    c.company_name,
    CONCAT(cont.first_name, ' ', cont.last_name) AS contact_name,
    o.stage,
    o.probability,
    o.amount,
    o.expected_close_date,
    CONCAT(u.first_name, ' ', u.last_name) AS sales_rep,
    o.product_category,
    DATEDIFF('day', o.created_date, CURRENT_DATE()) AS days_in_pipeline,
    CASE 
        WHEN o.expected_close_date < CURRENT_DATE() THEN 'Overdue'
        WHEN o.expected_close_date <= DATEADD('day', 30, CURRENT_DATE()) THEN 'Closing Soon'
        ELSE 'On Track'
    END AS urgency_status
FROM salesgenie_ai.crm_data.opportunities o
JOIN salesgenie_ai.crm_data.companies c ON o.company_id = c.company_id
LEFT JOIN salesgenie_ai.crm_data.contacts cont ON o.contact_id = cont.contact_id
LEFT JOIN salesgenie_ai.crm_data.sales_users u ON o.sales_rep = u.user_id
WHERE o.is_won = FALSE AND o.is_lost = FALSE;

-- Lead scoring view
CREATE OR REPLACE VIEW lead_insights AS
SELECT 
    l.lead_id,
    c.company_name,
    c.industry,
    c.company_size,
    c.annual_revenue,
    CONCAT(cont.first_name, ' ', cont.last_name) AS contact_name,
    cont.job_title,
    cont.decision_maker,
    l.lead_source,
    l.lead_status,
    l.lead_score,
    l.qualification_level,
    l.estimated_value,
    l.estimated_close_date,
    CONCAT(u.first_name, ' ', u.last_name) AS assigned_to,
    DATEDIFF('day', l.last_contacted_date, CURRENT_DATE()) AS days_since_contact,
    CASE 
        WHEN l.next_follow_up_date < CURRENT_DATE() THEN 'Overdue Follow-up'
        WHEN l.next_follow_up_date <= DATEADD('day', 3, CURRENT_DATE()) THEN 'Follow-up Soon'
        ELSE 'On Schedule'
    END AS follow_up_status
FROM salesgenie_ai.crm_data.leads l
JOIN salesgenie_ai.crm_data.companies c ON l.company_id = c.company_id
LEFT JOIN salesgenie_ai.crm_data.contacts cont ON l.contact_id = cont.contact_id
LEFT JOIN salesgenie_ai.crm_data.sales_users u ON l.assigned_to = u.user_id
WHERE l.is_converted = FALSE;

-- Sales performance view
CREATE OR REPLACE VIEW sales_performance AS
SELECT 
    u.user_id,
    CONCAT(u.first_name, ' ', u.last_name) AS sales_rep,
    u.territory,
    COUNT(DISTINCT o.opportunity_id) AS total_opportunities,
    COUNT(DISTINCT CASE WHEN o.is_won = TRUE THEN o.opportunity_id END) AS won_opportunities,
    COUNT(DISTINCT CASE WHEN o.is_lost = TRUE THEN o.opportunity_id END) AS lost_opportunities,
    SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) AS total_revenue,
    AVG(CASE WHEN o.is_won = TRUE THEN o.amount END) AS avg_deal_size,
    ROUND(COUNT(DISTINCT CASE WHEN o.is_won = TRUE THEN o.opportunity_id END) * 100.0 / 
          NULLIF(COUNT(DISTINCT o.opportunity_id), 0), 2) AS win_rate_percent
FROM salesgenie_ai.crm_data.sales_users u
LEFT JOIN salesgenie_ai.crm_data.opportunities o ON u.user_id = o.sales_rep
WHERE u.role = 'sales_rep'
GROUP BY u.user_id, u.first_name, u.last_name, u.territory;

COMMIT;
