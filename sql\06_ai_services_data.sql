/*
SalesGenie AI - AI Services Schema Data
Nihilent x Snowflake Hackathon 2025

This script populates the AI_SERVICES schema with additional tables and data
for AI model configurations, training data, and service monitoring.
*/

USE ROLE ACCOUNTADMIN;
USE WAREHOUSE salesgenie_ai_wh;
USE DATABASE salesgenie_ai;
USE SCHEMA salesgenie_ai.ai_services;

-- =====================================================
-- AI SERVICES ADDITIONAL TABLES
-- =====================================================

-- AI model configurations
CREATE OR REPLACE TABLE ai_model_configs (
    config_id STRING PRIMARY KEY,
    model_name STRING NOT NULL,
    model_type STRING, -- 'cortex_llm', 'cortex_analyst', 'cortex_search', 'custom_ml'
    model_version STRING,
    configuration VARIANT,
    performance_metrics VARIANT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Cortex service usage tracking
CREATE OR REPLACE TABLE cortex_usage_logs (
    usage_id STRING PRIMARY KEY,
    service_type STRING, -- 'complete', 'sentiment', 'summarize', 'translate', 'extract_answer'
    model_name STRING,
    input_tokens NUMBER(10,0),
    output_tokens NUMBER(10,0),
    request_timestamp TIMESTAMP_NTZ,
    response_time_ms NUMBER(10,0),
    user_id STRING,
    session_id STRING,
    cost_estimate NUMBER(10,4),
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- AI training datasets
CREATE OR REPLACE TABLE training_datasets (
    dataset_id STRING PRIMARY KEY,
    dataset_name STRING NOT NULL,
    dataset_type STRING, -- 'lead_scoring', 'sentiment_analysis', 'opportunity_prediction'
    data_source STRING,
    record_count NUMBER(10,0),
    feature_columns VARIANT,
    target_column STRING,
    data_quality_score NUMBER(5,4),
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    last_updated TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Model performance tracking
CREATE OR REPLACE TABLE model_performance (
    performance_id STRING PRIMARY KEY,
    model_config_id STRING,
    evaluation_date DATE,
    accuracy_score NUMBER(5,4),
    precision_score NUMBER(5,4),
    recall_score NUMBER(5,4),
    f1_score NUMBER(5,4),
    auc_score NUMBER(5,4),
    confusion_matrix VARIANT,
    feature_importance VARIANT,
    test_dataset_size NUMBER(10,0),
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- AI recommendations tracking
CREATE OR REPLACE TABLE ai_recommendations (
    recommendation_id STRING PRIMARY KEY,
    recommendation_type STRING, -- 'next_action', 'lead_priority', 'deal_risk', 'upsell_opportunity'
    entity_type STRING, -- 'lead', 'opportunity', 'customer'
    entity_id STRING,
    recommendation_text STRING,
    confidence_score NUMBER(5,4),
    model_used STRING,
    user_feedback STRING, -- 'helpful', 'not_helpful', 'partially_helpful'
    action_taken BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    feedback_date TIMESTAMP_NTZ
);

-- Semantic search configurations
CREATE OR REPLACE TABLE search_configurations (
    search_config_id STRING PRIMARY KEY,
    search_service_name STRING,
    index_name STRING,
    data_source_table STRING,
    search_columns VARIANT,
    embedding_model STRING,
    similarity_threshold NUMBER(5,4),
    max_results NUMBER(5,0),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- AI conversation history
CREATE OR REPLACE TABLE conversation_history (
    conversation_id STRING PRIMARY KEY,
    session_id STRING,
    user_id STRING,
    message_sequence NUMBER(5,0),
    user_message STRING,
    ai_response STRING,
    model_used STRING,
    response_time_ms NUMBER(10,0),
    satisfaction_rating NUMBER(1,0), -- 1-5 scale
    conversation_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- =====================================================
-- SAMPLE AI SERVICES DATA
-- =====================================================

-- Insert AI model configurations
INSERT INTO ai_model_configs (config_id, model_name, model_type, model_version, configuration, performance_metrics) VALUES
('config_001', 'claude-3-5-sonnet', 'cortex_llm', 'v1.0',
 PARSE_JSON('{"temperature": 0.7, "max_tokens": 2048, "top_p": 0.9, "use_case": "general_conversation"}'),
 PARSE_JSON('{"avg_response_time_ms": 1250, "accuracy_rating": 0.92, "user_satisfaction": 4.3}')),
('config_002', 'llama3.1-70b', 'cortex_llm', 'v1.0',
 PARSE_JSON('{"temperature": 0.5, "max_tokens": 1024, "top_p": 0.8, "use_case": "data_analysis"}'),
 PARSE_JSON('{"avg_response_time_ms": 980, "accuracy_rating": 0.89, "user_satisfaction": 4.1}')),
('config_003', 'mistral-large2', 'cortex_llm', 'v1.0',
 PARSE_JSON('{"temperature": 0.3, "max_tokens": 1536, "top_p": 0.85, "use_case": "technical_analysis"}'),
 PARSE_JSON('{"avg_response_time_ms": 1100, "accuracy_rating": 0.91, "user_satisfaction": 4.2}')),
('config_004', 'sales_crm_analyst', 'cortex_analyst', 'v2.1',
 PARSE_JSON('{"semantic_model": "sales_crm_semantic_model", "max_iterations": 3, "confidence_threshold": 0.8}'),
 PARSE_JSON('{"query_success_rate": 0.87, "avg_response_time_ms": 2100, "user_satisfaction": 4.4}')),
('config_005', 'lead_scoring_model', 'custom_ml', 'v1.2',
 PARSE_JSON('{"algorithm": "gradient_boosting", "features": 15, "training_samples": 10000}'),
 PARSE_JSON('{"accuracy": 0.875, "precision": 0.82, "recall": 0.89, "f1_score": 0.854}'));

-- Insert Cortex usage logs
INSERT INTO cortex_usage_logs (usage_id, service_type, model_name, input_tokens, output_tokens, request_timestamp, response_time_ms, user_id, session_id, cost_estimate) VALUES
('usage_001', 'complete', 'claude-3-5-sonnet', 150, 320, '2025-01-15 09:15:00', 1200, 'user_002', 'sess_001', 0.0045),
('usage_002', 'complete', 'llama3.1-70b', 89, 245, '2025-01-15 09:18:00', 950, 'user_003', 'sess_002', 0.0028),
('usage_003', 'sentiment', 'claude-3-5-sonnet', 45, 12, '2025-01-15 09:22:00', 450, 'user_002', 'sess_001', 0.0008),
('usage_004', 'complete', 'mistral-large2', 200, 380, '2025-01-15 09:25:00', 1100, 'user_004', 'sess_003', 0.0052),
('usage_005', 'summarize', 'claude-3-5-sonnet', 800, 150, '2025-01-15 09:30:00', 1800, 'user_002', 'sess_001', 0.0095),
('usage_006', 'complete', 'llama3.1-70b', 120, 290, '2025-01-15 09:35:00', 980, 'user_005', 'sess_004', 0.0035),
('usage_007', 'extract_answer', 'claude-3-5-sonnet', 300, 85, '2025-01-15 09:40:00', 1350, 'user_003', 'sess_002', 0.0042);

-- Insert training datasets
INSERT INTO training_datasets (dataset_id, dataset_name, dataset_type, data_source, record_count, feature_columns, target_column, data_quality_score) VALUES
('dataset_001', 'Lead Scoring Training Set Q4 2024', 'lead_scoring', 'salesgenie_ai.crm_data.leads', 5000,
 PARSE_JSON('["company_revenue", "industry", "company_size", "lead_source", "contact_title", "engagement_score", "website_visits", "email_opens"]'),
 'is_converted', 0.9200),
('dataset_002', 'Opportunity Prediction Dataset', 'opportunity_prediction', 'salesgenie_ai.crm_data.opportunities', 3500,
 PARSE_JSON('["amount", "stage", "probability", "days_in_pipeline", "company_revenue", "industry", "sales_rep_performance", "competitor"]'),
 'is_won', 0.8800),
('dataset_003', 'Customer Sentiment Analysis', 'sentiment_analysis', 'salesgenie_ai.crm_data.activities', 8000,
 PARSE_JSON('["activity_type", "description", "customer_tier", "interaction_channel", "resolution_time"]'),
 'sentiment_score', 0.9100),
('dataset_004', 'Churn Prediction Model Data', 'churn_prediction', 'salesgenie_ai.crm_data.companies', 2800,
 PARSE_JSON('["annual_revenue", "contract_value", "support_tickets", "last_contact_days", "product_usage", "satisfaction_score"]'),
 'churned', 0.8950),
('dataset_005', 'Sales Forecasting Historical', 'sales_forecasting', 'salesgenie_ai.analytics.historical_metrics', 12000,
 PARSE_JSON('["territory", "season", "economic_indicators", "team_size", "marketing_spend", "product_launches"]'),
 'monthly_revenue', 0.9350);

-- Insert model performance data
INSERT INTO model_performance (performance_id, model_config_id, evaluation_date, accuracy_score, precision_score, recall_score, f1_score, auc_score, test_dataset_size) VALUES
('perf_001', 'config_005', '2025-01-10', 0.8750, 0.8200, 0.8900, 0.8540, 0.9100, 1000),
('perf_002', 'config_005', '2025-01-05', 0.8650, 0.8100, 0.8850, 0.8460, 0.9050, 950),
('perf_003', 'config_005', '2024-12-30', 0.8580, 0.8050, 0.8800, 0.8410, 0.9000, 900),
('perf_004', 'config_004', '2025-01-12', 0.8900, 0.8500, 0.9200, 0.8840, 0.9250, 500),
('perf_005', 'config_004', '2025-01-08', 0.8850, 0.8450, 0.9150, 0.8790, 0.9200, 480);

-- Insert AI recommendations
INSERT INTO ai_recommendations (recommendation_id, recommendation_type, entity_type, entity_id, recommendation_text, confidence_score, model_used, user_feedback, action_taken) VALUES
('rec_001', 'next_action', 'lead', 'lead_001', 'Schedule demo call within 48 hours - high engagement detected', 0.8500, 'lead_scoring_model', 'helpful', TRUE),
('rec_002', 'deal_risk', 'opportunity', 'opp_001', 'Risk of stalling - competitor evaluation in progress, recommend executive meeting', 0.7800, 'opportunity_risk_model', 'helpful', TRUE),
('rec_003', 'lead_priority', 'lead', 'lead_003', 'High priority lead - enterprise budget confirmed, decision maker engaged', 0.9200, 'lead_scoring_model', 'helpful', TRUE),
('rec_004', 'upsell_opportunity', 'customer', 'comp_001', 'Cross-sell analytics module - customer showing interest in reporting features', 0.7200, 'upsell_model', 'partially_helpful', FALSE),
('rec_005', 'next_action', 'opportunity', 'opp_003', 'Send case study for similar healthcare implementation', 0.8100, 'content_recommendation_model', 'helpful', TRUE);

-- Insert search configurations
INSERT INTO search_configurations (search_config_id, search_service_name, index_name, data_source_table, search_columns, embedding_model, similarity_threshold, max_results) VALUES
('search_001', 'company_search_service', 'company_index', 'salesgenie_ai.crm_data.companies',
 PARSE_JSON('["company_name", "industry", "description", "website"]'), 'text-embedding-ada-002', 0.7500, 10),
('search_002', 'opportunity_search_service', 'opportunity_index', 'salesgenie_ai.crm_data.opportunities',
 PARSE_JSON('["opportunity_name", "description", "stage", "notes"]'), 'text-embedding-ada-002', 0.8000, 15),
('search_003', 'activity_search_service', 'activity_index', 'salesgenie_ai.crm_data.activities',
 PARSE_JSON('["subject", "description", "outcome"]'), 'text-embedding-ada-002', 0.7000, 20);

-- Insert conversation history
INSERT INTO conversation_history (conversation_id, session_id, user_id, message_sequence, user_message, ai_response, model_used, response_time_ms, satisfaction_rating) VALUES
('conv_001', 'sess_001', 'user_002', 1, 'Show me my pipeline by stage', 'Here is your current sales pipeline breakdown...', 'claude-3-5-sonnet', 1200, 5),
('conv_002', 'sess_001', 'user_002', 2, 'Which deals are at risk?', 'I found 2 high-risk opportunities that need attention...', 'claude-3-5-sonnet', 1350, 4),
('conv_003', 'sess_002', 'user_003', 1, 'Analyze my lead performance', 'Your lead conversion rate is 18.5%, here are the details...', 'llama3.1-70b', 950, 4),
('conv_004', 'sess_003', 'user_004', 1, 'What should I focus on today?', 'Based on your priorities, I recommend focusing on...', 'mistral-large2', 1100, 5),
('conv_005', 'sess_004', 'user_005', 1, 'Show me team performance metrics', 'Here is your sales team performance analysis...', 'claude-3-5-sonnet', 1450, 4);

COMMIT;
