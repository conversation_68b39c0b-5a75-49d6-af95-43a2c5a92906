from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:39,535 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:39,535 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 04:24:39,535 - DEBUG - Request guid: 22d3e2e4-713f-4be3-ac2f-61473afccb17
2025-05-29 04:24:39,535 - DEBUG - socket timeout: 60
2025-05-29 04:24:39,917 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=e1981ef9-362e-4613-89c5-f1d366f328a9&request_guid=22d3e2e4-713f-4be3-ac2f-61473afccb17 HTTP/1.1" 200 None
2025-05-29 04:24:39,933 - DEBUG - SUCCESS
2025-05-29 04:24:39,933 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:39,933 - DEBUG - ret[code] = None, after post request
2025-05-29 04:24:39,933 - DEBUG - Query id: 01bca8de-0205-6ab1-0000-000a0e5f733d
2025-05-29 04:24:39,933 - DEBUG - deserialize_json_dict() called: data from server: {'entries': [{'id': 0, 'timestamp': 1748472889941687, 'priority': 0, 'context': 'CMbV1oBw'}]}
2025-05-29 04:24:39,933 - DEBUG - Cache Entry: (0, 1748472888145544, 0)
2025-05-29 04:24:39,933 - DEBUG - deserialize {'id': 0, 'timestamp': 1748472889941687, 'priority': 0, 'context': 'CMbV1oBw'}
2025-05-29 04:24:39,933 - DEBUG - sync_priority_map called priority_map size = 0, new_priority_map size = 1
2025-05-29 04:24:39,933 - DEBUG - trim_cache() called. treeSet size is 1 and cache capacity is 5
2025-05-29 04:24:39,933 - DEBUG - trim_cache() returns. treeSet size is 1 and cache capacity is 5
2025-05-29 04:24:39,933 - DEBUG - deserialize_json_dict() returns  
2025-05-29 04:24:39,940 - DEBUG - Cache Entry: (0, 1748472889941687, 0)
2025-05-29 04:24:39,940 - DEBUG - sfqid: 01bca8de-0205-6ab1-0000-000a0e5f733d
2025-05-29 04:24:39,941 - DEBUG - query execution done
2025-05-29 04:24:39,941 - DEBUG - SUCCESS
2025-05-29 04:24:39,943 - DEBUG - PUT OR GET: False
2025-05-29 04:24:39,943 - DEBUG - Query result format: json        
2025-05-29 04:24:39,944 - DEBUG - parsing for result batch id: 1   
2025-05-29 04:24:39,945 - DEBUG - Number of results in first chunk: 1
2025-05-29 04:24:39,945 - DEBUG - Statement executed successfully  
2025-05-29 04:24:39,946 - DEBUG - Executing statement 17/17        
2025-05-29 04:24:39,946 - DEBUG - executing SQL/command
2025-05-29 04:24:39,946 - DEBUG - query: [COMMIT]
2025-05-29 04:24:39,946 - DEBUG - binding: [COMMIT] with input=[None], processed=[{}]
2025-05-29 04:24:39,946 - DEBUG - sequence counter: 23
2025-05-29 04:24:39,946 - DEBUG - Request id: 47470745-11a8-4dbc-b3fe-137a1dc03ef2
2025-05-29 04:24:39,946 - DEBUG - running query [COMMIT]
2025-05-29 04:24:39,946 - DEBUG - is_file_transfer: True
2025-05-29 04:24:39,946 - DEBUG - _cmd_query
2025-05-29 04:24:39,949 - DEBUG - serialize_to_dict() called       
2025-05-29 04:24:39,949 - DEBUG - Cache Entry: (0, 1748472889941687, 0)
2025-05-29 04:24:39,950 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748472889941687, 'priority': 0, 'context': {'base64Data': 'CMbV1oBw'}}]}
2025-05-29 04:24:39,950 - DEBUG - sql=[COMMIT], sequence_id=[23], is_file_transfer=[False]
2025-05-29 04:24:39,950 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:39,950 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:39,952 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 04:24:39,952 - DEBUG - Request guid: 1f918fb3-47cd-4cd3-85cc-b174630e2281
2025-05-29 04:24:39,952 - DEBUG - socket timeout: 60
2025-05-29 04:24:40,293 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=47470745-11a8-4dbc-b3fe-137a1dc03ef2&request_guid=1f918fb3-47cd-4cd3-85cc-b174630e2281 HTTP/1.1" 200 None
2025-05-29 04:24:40,293 - DEBUG - SUCCESS
2025-05-29 04:24:40,293 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:40,293 - DEBUG - ret[code] = None, after post request
2025-05-29 04:24:40,293 - DEBUG - Query id: 01bca8de-0205-6cf9-0000-000a0e5fe18d
2025-05-29 04:24:40,293 - DEBUG - deserialize_json_dict() called: data from server: {'entries': [{'id': 0, 'timestamp': 1748472890360377, 'priority': 0, 'context': 'CObn1oBw'}]}
2025-05-29 04:24:40,293 - DEBUG - Cache Entry: (0, 1748472889941687, 0)
2025-05-29 04:24:40,293 - DEBUG - deserialize {'id': 0, 'timestamp': 1748472890360377, 'priority': 0, 'context': 'CObn1oBw'}
2025-05-29 04:24:40,293 - DEBUG - sync_priority_map called priority_map size = 0, new_priority_map size = 1
2025-05-29 04:24:40,293 - DEBUG - trim_cache() called. treeSet size is 1 and cache capacity is 5
2025-05-29 04:24:40,293 - DEBUG - trim_cache() returns. treeSet size is 1 and cache capacity is 5
2025-05-29 04:24:40,293 - DEBUG - deserialize_json_dict() returns  
2025-05-29 04:24:40,293 - DEBUG - Cache Entry: (0, 1748472890360377, 0)
2025-05-29 04:24:40,293 - DEBUG - sfqid: 01bca8de-0205-6cf9-0000-000a0e5fe18d
2025-05-29 04:24:40,293 - DEBUG - query execution done
2025-05-29 04:24:40,293 - DEBUG - SUCCESS
2025-05-29 04:24:40,293 - DEBUG - PUT OR GET: False
2025-05-29 04:24:40,293 - DEBUG - Query result format: json        
2025-05-29 04:24:40,293 - DEBUG - parsing for result batch id: 1   
2025-05-29 04:24:40,293 - DEBUG - Number of results in first chunk: 1
2025-05-29 04:24:40,293 - DEBUG - Statement executed successfully  
2025-05-29 04:24:40,293 - INFO - Successfully executed SQL file: sql/02_sample_data.sql
2025-05-29 04:24:40,293 - INFO - Executing SQL file: sql/03_ai_services_setup.sql
2025-05-29 04:24:40,293 - DEBUG - cursor
2025-05-29 04:24:40,293 - DEBUG - Executing statement 2/19
2025-05-29 04:24:40,293 - DEBUG - executing SQL/command
2025-05-29 04:24:40,293 - DEBUG - query: [USE WAREHOUSE salesgenie_ai_wh]
2025-05-29 04:24:40,293 - DEBUG - binding: [USE WAREHOUSE salesgenie_ai_wh] with input=[None], processed=[{}]
2025-05-29 04:24:40,293 - DEBUG - sequence counter: 24
2025-05-29 04:24:40,293 - DEBUG - Request id: 6bba7e2c-4325-4790-a060-9b19231f01f1
2025-05-29 04:24:40,293 - DEBUG - running query [USE WAREHOUSE salesgenie_ai_wh]
2025-05-29 04:24:40,293 - DEBUG - is_file_transfer: True
2025-05-29 04:24:40,293 - DEBUG - _cmd_query
2025-05-29 04:24:40,293 - DEBUG - serialize_to_dict() called       
2025-05-29 04:24:40,293 - DEBUG - Cache Entry: (0, 1748472890360377, 0)
2025-05-29 04:24:40,308 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748472890360377, 'priority': 0, 'context': {'base64Data': 'CObn1oBw'}}]}
2025-05-29 04:24:40,308 - DEBUG - sql=[USE WAREHOUSE salesgenie_ai_wh], sequence_id=[24], is_file_transfer=[False]
2025-05-29 04:24:40,308 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:40,308 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:40,311 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 04:24:40,311 - DEBUG - Request guid: d7fb0b2b-a8f8-425d-aff2-72605ef71c6b
2025-05-29 04:24:40,311 - DEBUG - socket timeout: 60
2025-05-29 04:24:40,778 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=6bba7e2c-4325-4790-a060-9b19231f01f1&request_guid=d7fb0b2b-a8f8-425d-aff2-72605ef71c6b HTTP/1.1" 200 None
2025-05-29 04:24:40,779 - DEBUG - SUCCESS
2025-05-29 04:24:40,779 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:40,779 - DEBUG - ret[code] = 002043, after post request
2025-05-29 04:24:40,779 - DEBUG - Query id: 01bca8de-0205-6ab1-0000-000a0e5f7341
2025-05-29 04:24:40,779 - DEBUG - sfqid: 01bca8de-0205-6ab1-0000-000a0e5f7341
2025-05-29 04:24:40,779 - DEBUG - query execution done
2025-05-29 04:24:40,779 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002043', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8de-0205-6ab1-0000-000a0e5f7341', 'line': -1, 'pos': -1, 'type': 'COMPILATION'}, 'code': '002043', 'message': 'SQL compilation error:\nObject does not exist, or operation cannot be performed.', 'success': False, 'headers': None}
2025-05-29 04:24:40,779 - WARNING - Statement 2 failed: 002043 (02000): 01bca8de-0205-6ab1-0000-000a0e5f7341: SQL compilation error:  
Object does not exist, or operation cannot be performed.
2025-05-29 04:24:40,779 - DEBUG - Executing statement 3/19
2025-05-29 04:24:40,779 - DEBUG - executing SQL/command
2025-05-29 04:24:40,779 - DEBUG - query: [USE DATABASE salesgenie_ai]
2025-05-29 04:24:40,779 - DEBUG - binding: [USE DATABASE salesgenie_ai] with input=[None], processed=[{}]
2025-05-29 04:24:40,779 - DEBUG - sequence counter: 25
2025-05-29 04:24:40,779 - DEBUG - Request id: 540b2ffa-cb1f-4d9e-9a65-e5184288fc18
2025-05-29 04:24:40,779 - DEBUG - running query [USE DATABASE salesgenie_ai]
2025-05-29 04:24:40,779 - DEBUG - is_file_transfer: True
2025-05-29 04:24:40,779 - DEBUG - _cmd_query
2025-05-29 04:24:40,779 - DEBUG - serialize_to_dict() called       
2025-05-29 04:24:40,779 - DEBUG - Cache Entry: (0, 1748472890360377, 0)
2025-05-29 04:24:40,779 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748472890360377, 'priority': 0, 'context': {'base64Data': 'CObn1oBw'}}]}
2025-05-29 04:24:40,785 - DEBUG - sql=[USE DATABASE salesgenie_ai], sequence_id=[25], is_file_transfer=[False]
2025-05-29 04:24:40,788 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:40,789 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:40,790 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 04:24:40,790 - DEBUG - Request guid: 19ad13d6-d210-457b-98bd-e3e797974477
2025-05-29 04:24:40,790 - DEBUG - socket timeout: 60
2025-05-29 04:24:41,245 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=540b2ffa-cb1f-4d9e-9a65-e5184288fc18&request_guid=19ad13d6-d210-457b-98bd-e3e797974477 HTTP/1.1" 200 None
2025-05-29 04:24:41,251 - DEBUG - SUCCESS
2025-05-29 04:24:41,255 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:41,255 - DEBUG - ret[code] = 002043, after post request
2025-05-29 04:24:41,257 - DEBUG - Query id: 01bca8de-0205-68ee-0000-000a0e5f842d
2025-05-29 04:24:41,258 - DEBUG - sfqid: 01bca8de-0205-68ee-0000-000a0e5f842d
2025-05-29 04:24:41,266 - DEBUG - query execution done
2025-05-29 04:24:41,268 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002043', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8de-0205-68ee-0000-000a0e5f842d', 'line': -1, 'pos': -1, 'type': 'COMPILATION'}, 'code': '002043', 'message': 'SQL compilation error:\nObject does not exist, or operation cannot be performed.', 'success': False, 'headers': None}
2025-05-29 04:24:41,272 - WARNING - Statement 3 failed: 002043 (02000): 01bca8de-0205-68ee-0000-000a0e5f842d: SQL compilation error:  
Object does not exist, or operation cannot be performed.
2025-05-29 04:24:41,286 - DEBUG - Executing statement 16/19        
2025-05-29 04:24:41,298 - DEBUG - executing SQL/command
2025-05-29 04:24:41,304 - DEBUG - query: [COMMIT]
2025-05-29 04:24:41,318 - DEBUG - binding: [COMMIT] with input=[None], processed=[{}]
2025-05-29 04:24:41,325 - DEBUG - sequence counter: 26
2025-05-29 04:24:41,335 - DEBUG - Request id: 4df47d54-cf6f-42c7-947a-647f7260ee39
2025-05-29 04:24:41,335 - DEBUG - running query [COMMIT]
2025-05-29 04:24:41,335 - DEBUG - is_file_transfer: True
2025-05-29 04:24:41,347 - DEBUG - _cmd_query
2025-05-29 04:24:41,347 - DEBUG - serialize_to_dict() called       
2025-05-29 04:24:41,350 - DEBUG - Cache Entry: (0, 1748472890360377, 0)
2025-05-29 04:24:41,358 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748472890360377, 'priority': 0, 'context': {'base64Data': 'CObn1oBw'}}]}
2025-05-29 04:24:41,364 - DEBUG - sql=[COMMIT], sequence_id=[26], is_file_transfer=[False]
2025-05-29 04:24:41,368 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:41,370 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:41,374 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 04:24:41,386 - DEBUG - Request guid: defc8cb2-bb88-447b-a4e5-8bb2f7944357
2025-05-29 04:24:41,388 - DEBUG - socket timeout: 60
2025-05-29 04:24:41,793 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=4df47d54-cf6f-42c7-947a-647f7260ee39&request_guid=defc8cb2-bb88-447b-a4e5-8bb2f7944357 HTTP/1.1" 200 None
2025-05-29 04:24:41,795 - DEBUG - SUCCESS
2025-05-29 04:24:41,796 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:41,796 - DEBUG - ret[code] = None, after post request
2025-05-29 04:24:41,798 - DEBUG - Query id: 01bca8de-0205-6cf9-0000-000a0e5fe191
2025-05-29 04:24:41,798 - DEBUG - deserialize_json_dict() called: data from server: {'entries': [{'id': 0, 'timestamp': 1748472891851500, 'priority': 0, 'context': 'CObn1oBw'}]}
2025-05-29 04:24:41,798 - DEBUG - Cache Entry: (0, 1748472890360377, 0)
2025-05-29 04:24:41,798 - DEBUG - deserialize {'id': 0, 'timestamp': 1748472891851500, 'priority': 0, 'context': 'CObn1oBw'}
2025-05-29 04:24:41,798 - DEBUG - sync_priority_map called priority_map size = 0, new_priority_map size = 1
2025-05-29 04:24:41,798 - DEBUG - trim_cache() called. treeSet size is 1 and cache capacity is 5
2025-05-29 04:24:41,800 - DEBUG - trim_cache() returns. treeSet size is 1 and cache capacity is 5
2025-05-29 04:24:41,800 - DEBUG - deserialize_json_dict() returns  
2025-05-29 04:24:41,800 - DEBUG - Cache Entry: (0, 1748472891851500, 0)
2025-05-29 04:24:41,801 - DEBUG - sfqid: 01bca8de-0205-6cf9-0000-000a0e5fe191
2025-05-29 04:24:41,801 - DEBUG - query execution done
2025-05-29 04:24:41,801 - DEBUG - SUCCESS
2025-05-29 04:24:41,801 - DEBUG - PUT OR GET: False
2025-05-29 04:24:41,801 - DEBUG - Query result format: json        
2025-05-29 04:24:41,801 - DEBUG - parsing for result batch id: 1   
2025-05-29 04:24:41,801 - DEBUG - Number of results in first chunk: 1
2025-05-29 04:24:41,803 - DEBUG - Statement executed successfully  
2025-05-29 04:24:41,803 - INFO - Successfully executed SQL file: sql/03_ai_services_setup.sql
2025-05-29 04:24:41,803 - INFO - Executing SQL file: sql/04_advanced_analytics.sql
2025-05-29 04:24:41,804 - DEBUG - cursor
2025-05-29 04:24:41,808 - DEBUG - Executing statement 2/12
2025-05-29 04:24:41,808 - DEBUG - executing SQL/command
2025-05-29 04:24:41,809 - DEBUG - query: [USE WAREHOUSE salesgenie_ai_wh]
2025-05-29 04:24:41,809 - DEBUG - binding: [USE WAREHOUSE salesgenie_ai_wh] with input=[None], processed=[{}]
2025-05-29 04:24:41,809 - DEBUG - sequence counter: 27
2025-05-29 04:24:41,811 - DEBUG - Request id: 24f0ccfd-b0b4-4dd4-aaf9-349e22069553
2025-05-29 04:24:41,811 - DEBUG - running query [USE WAREHOUSE salesgenie_ai_wh]
2025-05-29 04:24:41,813 - DEBUG - is_file_transfer: True
2025-05-29 04:24:41,814 - DEBUG - _cmd_query
2025-05-29 04:24:41,815 - DEBUG - serialize_to_dict() called       
2025-05-29 04:24:41,815 - DEBUG - Cache Entry: (0, 1748472891851500, 0)
2025-05-29 04:24:41,816 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748472891851500, 'priority': 0, 'context': {'base64Data': 'CObn1oBw'}}]}
2025-05-29 04:24:41,816 - DEBUG - sql=[USE WAREHOUSE salesgenie_ai_wh], sequence_id=[27], is_file_transfer=[False]
2025-05-29 04:24:41,816 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:41,816 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:41,816 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 04:24:41,816 - DEBUG - Request guid: 401012de-f7db-45c1-82f1-8b1ebb61b062
2025-05-29 04:24:41,816 - DEBUG - socket timeout: 60
2025-05-29 04:24:42,172 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=24f0ccfd-b0b4-4dd4-aaf9-349e22069553&request_guid=401012de-f7db-45c1-82f1-8b1ebb61b062 HTTP/1.1" 200 None
2025-05-29 04:24:42,172 - DEBUG - SUCCESS
2025-05-29 04:24:42,172 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:42,172 - DEBUG - ret[code] = 002043, after post request
2025-05-29 04:24:42,172 - DEBUG - Query id: 01bca8de-0205-696d-0000-000a0e5fa295
2025-05-29 04:24:42,172 - DEBUG - sfqid: 01bca8de-0205-696d-0000-000a0e5fa295
2025-05-29 04:24:42,172 - DEBUG - query execution done
2025-05-29 04:24:42,188 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002043', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8de-0205-696d-0000-000a0e5fa295', 'line': -1, 'pos': -1, 'type': 'COMPILATION'}, 'code': '002043', 'message': 'SQL compilation error:\nObject does not exist, or operation cannot be performed.', 'success': False, 'headers': None}
2025-05-29 04:24:42,188 - WARNING - Statement 2 failed: 002043 (02000): 01bca8de-0205-696d-0000-000a0e5fa295: SQL compilation error:  
Object does not exist, or operation cannot be performed.
2025-05-29 04:24:42,188 - DEBUG - Executing statement 3/12
2025-05-29 04:24:42,188 - DEBUG - executing SQL/command
2025-05-29 04:24:42,188 - DEBUG - query: [USE DATABASE salesgenie_ai]
2025-05-29 04:24:42,188 - DEBUG - binding: [USE DATABASE salesgenie_ai] with input=[None], processed=[{}]
2025-05-29 04:24:42,188 - DEBUG - sequence counter: 28
2025-05-29 04:24:42,191 - DEBUG - Request id: 48c60d5c-824c-4c5c-b1d3-5d9a8ecf3fcd
2025-05-29 04:24:42,191 - DEBUG - running query [USE DATABASE salesgenie_ai]
2025-05-29 04:24:42,191 - DEBUG - is_file_transfer: True
2025-05-29 04:24:42,192 - DEBUG - _cmd_query
2025-05-29 04:24:42,192 - DEBUG - serialize_to_dict() called       
2025-05-29 04:24:42,192 - DEBUG - Cache Entry: (0, 1748472891851500, 0)
2025-05-29 04:24:42,192 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748472891851500, 'priority': 0, 'context': {'base64Data': 'CObn1oBw'}}]}
2025-05-29 04:24:42,193 - DEBUG - sql=[USE DATABASE salesgenie_ai], sequence_id=[28], is_file_transfer=[False]
2025-05-29 04:24:42,193 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:42,193 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:42,193 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 04:24:42,249 - DEBUG - Request guid: ed996822-30d0-4971-b787-1716f5ed8774
2025-05-29 04:24:42,282 - DEBUG - socket timeout: 60
2025-05-29 04:24:42,696 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=48c60d5c-824c-4c5c-b1d3-5d9a8ecf3fcd&request_guid=ed996822-30d0-4971-b787-1716f5ed8774 HTTP/1.1" 200 None
2025-05-29 04:24:42,696 - DEBUG - SUCCESS
2025-05-29 04:24:42,696 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:42,696 - DEBUG - ret[code] = 002043, after post request
2025-05-29 04:24:42,696 - DEBUG - Query id: 01bca8de-0205-6cf8-0000-000a0e5fd14d
2025-05-29 04:24:42,696 - DEBUG - sfqid: 01bca8de-0205-6cf8-0000-000a0e5fd14d
2025-05-29 04:24:42,696 - DEBUG - query execution done
2025-05-29 04:24:42,700 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002043', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8de-0205-6cf8-0000-000a0e5fd14d', 'line': -1, 'pos': -1, 'type': 'COMPILATION'}, 'code': '002043', 'message': 'SQL compilation error:\nObject does not exist, or operation cannot be performed.', 'success': False, 'headers': None}
2025-05-29 04:24:42,700 - WARNING - Statement 3 failed: 002043 (02000): 01bca8de-0205-6cf8-0000-000a0e5fd14d: SQL compilation error:  
Object does not exist, or operation cannot be performed.
2025-05-29 04:24:42,700 - DEBUG - Executing statement 4/12
2025-05-29 04:24:42,700 - DEBUG - executing SQL/command
2025-05-29 04:24:42,700 - DEBUG - query: [USE SCHEMA salesgenie_ai.analytics]
2025-05-29 04:24:42,700 - DEBUG - binding: [USE SCHEMA salesgenie_ai.analytics] with input=[None], processed=[{}]
2025-05-29 04:24:42,700 - DEBUG - sequence counter: 29
2025-05-29 04:24:42,700 - DEBUG - Request id: 06f61475-5f2b-4fbd-838a-84c97b78edeb
2025-05-29 04:24:42,700 - DEBUG - running query [USE SCHEMA salesgenie_ai.analytics]
2025-05-29 04:24:42,700 - DEBUG - is_file_transfer: True
2025-05-29 04:24:42,700 - DEBUG - _cmd_query
2025-05-29 04:24:42,700 - DEBUG - serialize_to_dict() called       
2025-05-29 04:24:42,700 - DEBUG - Cache Entry: (0, 1748472891851500, 0)
2025-05-29 04:24:42,704 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748472891851500, 'priority': 0, 'context': {'base64Data': 'CObn1oBw'}}]}
2025-05-29 04:24:42,704 - DEBUG - sql=[USE SCHEMA salesgenie_ai.analytics], sequence_id=[29], is_file_transfer=[False]
2025-05-29 04:24:42,704 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:42,704 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:42,704 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 04:24:42,704 - DEBUG - Request guid: 32141d17-196b-4650-abfd-3a8cfb24c7ef
2025-05-29 04:24:42,704 - DEBUG - socket timeout: 60
2025-05-29 04:24:43,087 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=06f61475-5f2b-4fbd-838a-84c97b78edeb&request_guid=32141d17-196b-4650-abfd-3a8cfb24c7ef HTTP/1.1" 200 None
2025-05-29 04:24:43,087 - DEBUG - SUCCESS
2025-05-29 04:24:43,087 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:43,087 - DEBUG - ret[code] = 002043, after post request
2025-05-29 04:24:43,087 - DEBUG - Query id: 01bca8de-0205-6ab1-0000-000a0e5f7345
2025-05-29 04:24:43,087 - DEBUG - sfqid: 01bca8de-0205-6ab1-0000-000a0e5f7345
2025-05-29 04:24:43,087 - DEBUG - query execution done
2025-05-29 04:24:43,087 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002043', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8de-0205-6ab1-0000-000a0e5f7345', 'line': -1, 'pos': -1, 'type': 'COMPILATION'}, 'code': '002043', 'message': 'SQL compilation error:\nObject does not exist, or operation cannot be performed.', 'success': False, 'headers': None}
2025-05-29 04:24:43,087 - WARNING - Statement 4 failed: 002043 (02000): 01bca8de-0205-6ab1-0000-000a0e5f7345: SQL compilation error:  
Object does not exist, or operation cannot be performed.
2025-05-29 04:24:43,087 - DEBUG - Executing statement 12/12        
2025-05-29 04:24:43,087 - DEBUG - executing SQL/command
2025-05-29 04:24:43,087 - DEBUG - query: [COMMIT]
2025-05-29 04:24:43,087 - DEBUG - binding: [COMMIT] with input=[None], processed=[{}]
2025-05-29 04:24:43,087 - DEBUG - sequence counter: 30
2025-05-29 04:24:43,087 - DEBUG - Request id: ddbf5bf1-3381-421b-b401-88a99b643458
2025-05-29 04:24:43,087 - DEBUG - running query [COMMIT]
2025-05-29 04:24:43,087 - DEBUG - is_file_transfer: True
2025-05-29 04:24:43,087 - DEBUG - _cmd_query
2025-05-29 04:24:43,087 - DEBUG - serialize_to_dict() called       
2025-05-29 04:24:43,087 - DEBUG - Cache Entry: (0, 1748472891851500, 0)
2025-05-29 04:24:43,087 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748472891851500, 'priority': 0, 'context': {'base64Data': 'CObn1oBw'}}]}
2025-05-29 04:24:43,087 - DEBUG - sql=[COMMIT], sequence_id=[30], is_file_transfer=[False]
2025-05-29 04:24:43,097 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:43,098 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:43,098 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 04:24:43,098 - DEBUG - Request guid: c9a3bb79-bab3-4620-835e-5c254cc0abcc
2025-05-29 04:24:43,098 - DEBUG - socket timeout: 60
2025-05-29 04:24:43,525 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=ddbf5bf1-3381-421b-b401-88a99b643458&request_guid=c9a3bb79-bab3-4620-835e-5c254cc0abcc HTTP/1.1" 200 None
2025-05-29 04:24:43,527 - DEBUG - SUCCESS
2025-05-29 04:24:43,527 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:43,527 - DEBUG - ret[code] = None, after post request
2025-05-29 04:24:43,527 - DEBUG - Query id: 01bca8de-0205-6cf9-0000-000a0e5fe195
2025-05-29 04:24:43,527 - DEBUG - deserialize_json_dict() called: data from server: {'entries': [{'id': 0, 'timestamp': 1748472893584050, 'priority': 0, 'context': 'CObn1oBw'}]}
2025-05-29 04:24:43,527 - DEBUG - Cache Entry: (0, 1748472891851500, 0)
2025-05-29 04:24:43,529 - DEBUG - deserialize {'id': 0, 'timestamp': 1748472893584050, 'priority': 0, 'context': 'CObn1oBw'}
2025-05-29 04:24:43,529 - DEBUG - sync_priority_map called priority_map size = 0, new_priority_map size = 1
2025-05-29 04:24:43,529 - DEBUG - trim_cache() called. treeSet size is 1 and cache capacity is 5
2025-05-29 04:24:43,529 - DEBUG - trim_cache() returns. treeSet size is 1 and cache capacity is 5
2025-05-29 04:24:43,529 - DEBUG - deserialize_json_dict() returns  
2025-05-29 04:24:43,529 - DEBUG - Cache Entry: (0, 1748472893584050, 0)
2025-05-29 04:24:43,529 - DEBUG - sfqid: 01bca8de-0205-6cf9-0000-000a0e5fe195
2025-05-29 04:24:43,529 - DEBUG - query execution done
2025-05-29 04:24:43,529 - DEBUG - SUCCESS
2025-05-29 04:24:43,529 - DEBUG - PUT OR GET: False
2025-05-29 04:24:43,529 - DEBUG - Query result format: json        
2025-05-29 04:24:43,529 - DEBUG - parsing for result batch id: 1   
2025-05-29 04:24:43,529 - DEBUG - Number of results in first chunk: 1
2025-05-29 04:24:43,529 - DEBUG - Statement executed successfully  
2025-05-29 04:24:43,529 - INFO - Successfully executed SQL file: sql/04_advanced_analytics.sql
2025-05-29 04:24:43,532 - INFO - Uploading semantic model...       
2025-05-29 04:24:43,532 - DEBUG - cursor
2025-05-29 04:24:43,533 - DEBUG - executing SQL/command
2025-05-29 04:24:43,533 - DEBUG - query: [CREATE STAGE IF NOT EXISTS salesgenie_ai.ai_services.semantic_models DIRECTORY =...]        
2025-05-29 04:24:43,533 - DEBUG - binding: [CREATE STAGE IF NOT EXISTS salesgenie_ai.ai_services.semantic_models DIRECTORY =...] with input=[None], processed=[{}]
2025-05-29 04:24:43,533 - DEBUG - sequence counter: 31
2025-05-29 04:24:43,533 - DEBUG - Request id: b8c27d05-1505-41f5-948f-7a011e9721f0
2025-05-29 04:24:43,533 - DEBUG - running query [CREATE STAGE IF NOT EXISTS salesgenie_ai.ai_services.semantic_models DIRECTORY =...] 
2025-05-29 04:24:43,533 - DEBUG - is_file_transfer: True
2025-05-29 04:24:43,534 - DEBUG - _cmd_query
2025-05-29 04:24:43,534 - DEBUG - serialize_to_dict() called       
2025-05-29 04:24:43,534 - DEBUG - Cache Entry: (0, 1748472893584050, 0)
2025-05-29 04:24:43,535 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748472893584050, 'priority': 0, 'context': {'base64Data': 'CObn1oBw'}}]}
2025-05-29 04:24:43,535 - DEBUG - sql=[CREATE STAGE IF NOT EXISTS salesgenie_ai.ai_services.semantic_models DIRECTORY =...], sequence_id=[31], is_file_transfer=[False]
2025-05-29 04:24:43,536 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:43,536 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:43,538 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 04:24:43,538 - DEBUG - Request guid: 89036b1f-5190-429b-a9d7-a60be88d854b
2025-05-29 04:24:43,538 - DEBUG - socket timeout: 60
2025-05-29 04:24:43,892 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=b8c27d05-1505-41f5-948f-7a011e9721f0&request_guid=89036b1f-5190-429b-a9d7-a60be88d854b HTTP/1.1" 200 None
2025-05-29 04:24:43,896 - DEBUG - SUCCESS
2025-05-29 04:24:43,896 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:43,896 - DEBUG - ret[code] = 002003, after post request
2025-05-29 04:24:43,896 - DEBUG - Query id: 01bca8de-0205-696d-0000-000a0e5fa299
2025-05-29 04:24:43,896 - DEBUG - sfqid: 01bca8de-0205-696d-0000-000a0e5fa299
2025-05-29 04:24:43,896 - DEBUG - query execution done
2025-05-29 04:24:43,896 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002003', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8de-0205-696d-0000-000a0e5fa299', 'line': 1, 'pos': 27, 'type': 'COMPILATION'}, 'code': '002003', 'message': "SQL compilation error:\nDatabase 'SALESGENIE_AI' does not exist or not authorized.", 'success': False, 'headers': None}
2025-05-29 04:24:43,898 - ERROR - Failed to upload semantic model: 002003 (02000): 01bca8de-0205-696d-0000-000a0e5fa299: SQL compilation error:
Database 'SALESGENIE_AI' does not exist or not authorized.
2025-05-29 04:24:43,898 - ERROR - Semantic model upload failed     
2025-05-29 04:24:43,898 - DEBUG - closed
2025-05-29 04:24:43,898 - DEBUG - Closing telemetry client.        
2025-05-29 04:24:43,902 - DEBUG - Sending 1 logs to telemetry. Data is {'logs': [{'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_imported_packages', 'value': "{'webbrowser', 'idna', 'platformdirs', 'dateutil', 'six', 're', 'requests', 'nturl2path', 'boto3', 'struct', 'time', 'zlib', 'sysconfig', 'ntpath', 'queue', 'locale', 'configparser', 'charset_normalizer', 'shlex', 'encodings', 'argparse', 'subprocess', 'site', 'sys', 'random', 'warnings', 'asn1crypto', 'urllib', 'copy', 'packaging', 'socket', 'sre_constants', 'hashlib', 'base64', 'weakref', 'dataclasses', 'threading', 'inspect', 'ctypes', 'posixpath', 'html', 'decimal', 'bisect', 'xml', 'builtins', 'platform', 'reprlib', 'os', 'secrets', 'typing_extensions', 'opcode', 'asyncio', 'marshal', 'calendar', 'textwrap', 'copyreg', 'logging', 'gzip', 'codecs', 'contextvars', 'numbers', 'fractions', 'io', 'bz2', 'collections', 'linecache', 'getpass', 'certifi', 'errno', 'json', 'types', 'traceback', 'sortedcontainers', 'ssl', 'ipaddress', 'ast', 'gc', 'functools', 'contextlib', 'pytz', 'jmespath', 'select', 'concurrent', 'datetime', 'tokenize', 'shutil', 'OpenSSL', 'operator', 'selectors', 'keyword', 'tempfile', 'importlib', 'signal', 'nt', 'pyexpat', 'string', 'dis', 'jwt', 'token', 'binascii', 'winreg', 'zipimport', 'msvcrt', 'unicodedata', 'pickle', 'http', 'lzma', 'email', 'genericpath', 'mimetypes', 'enum', 'csv', 'uu', 'hmac', 'stringprep', 'sre_compile', 'stat', 'fnmatch', 'zipfile', 'cython_runtime', 'sre_parse', 'difflib', 'pathlib', 'atexit', 'heapq', 'math', 'cryptography', 'urllib3', 'filelock', 'typing', 'botocore', 'itertools', 'gettext', 'quopri', 'abc', 'snowflake', 'tomlkit', 'uuid'}"}, 'timestamp': '1748472869554'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8de-0205-68ee-0000-000a0e5f8421', 'value': -10002}, 'timestamp': '1748472870612'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8de-0205-6cf8-0000-000a0e5fd141', 'value': -10007}, 'timestamp': '1748472871032'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6ab1-0000-000a0e5f7335', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472871477'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-67e9-0000-000a0e5f43cd', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472871865'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-67fa-0000-000a0e5f9599', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472872461'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6cf8-0000-000a0e5fd145', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472872968'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-68ee-0000-000a0e5f8425', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472873379'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6eec-0000-000a0e5ff129', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472873754'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-67fa-0000-000a0e5f959d', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472874199'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6cf9-0000-000a0e5fe17d', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472874610'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6eec-0000-000a0e5ff12d', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472874961'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6cf9-0000-000a0e5fe181', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472875365'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6eec-0000-000a0e5ff131', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472875827'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-68ee-0000-000a0e5f8429', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472876237'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-67fa-0000-000a0e5f95a1', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472876652'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6cf9-0000-000a0e5fe185', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472877029'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6cf8-0000-000a0e5fd149', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472877679'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8de-0205-6ab1-0000-000a0e5f7339', 'value': -9972}, 'timestamp': '1748472878193'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-67fa-0000-000a0e5f95a5', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472878602'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6cf9-0000-000a0e5fe189', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472879016'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-696d-0000-000a0e5fa291', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472879528'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8de-0205-6ab1-0000-000a0e5f733d', 'value': -10010}, 'timestamp': '1748472879941'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8de-0205-6cf9-0000-000a0e5fe18d', 'value': -10074}, 'timestamp': '1748472880293'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6ab1-0000-000a0e5f7341', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472880779'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-68ee-0000-000a0e5f842d', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472881272'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8de-0205-6cf9-0000-000a0e5fe191', 'value': -10056}, 'timestamp': '1748472881801'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-696d-0000-000a0e5fa295', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472882188'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6cf8-0000-000a0e5fd14d', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472882700'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-6ab1-0000-000a0e5f7345', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472883087'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8de-0205-6cf9-0000-000a0e5fe195', 'value': -10063}, 'timestamp': '1748472883529'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8de-0205-696d-0000-000a0e5fa299', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748472883898'}]}.
2025-05-29 04:24:43,907 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 04:24:43,907 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:43,909 - DEBUG - remaining request timeout: 5000 ms, retry cnt: 1
2025-05-29 04:24:43,909 - DEBUG - Request guid: 365adca6-84b1-496f-90e3-f70767bb2181
2025-05-29 04:24:43,911 - DEBUG - socket timeout: 60
2025-05-29 04:24:44,225 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /telemetry/send?request_guid=365adca6-84b1-496f-90e3-f70767bb2181 HTTP/1.1" 200 None
2025-05-29 04:24:44,237 - DEBUG - SUCCESS
2025-05-29 04:24:44,237 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:44,237 - DEBUG - ret[code] = None, after post request
2025-05-29 04:24:44,237 - DEBUG - Successfully uploading metrics to telemetry.
2025-05-29 04:24:44,239 - DEBUG - No async queries seem to be running, deleting session
2025-05-29 04:24:44,239 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 04:24:44,239 - DEBUG - remaining request timeout: 5000 ms, retry cnt: 1
2025-05-29 04:24:44,239 - DEBUG - Request guid: bb4e6f31-1583-4f41-ad3a-517024080f01
2025-05-29 04:24:44,239 - DEBUG - socket timeout: 60
2025-05-29 04:24:44,651 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /session?delete=true&request_guid=bb4e6f31-1583-4f41-ad3a-517024080f01 HTTP/1.1" 200 None
2025-05-29 04:24:44,651 - DEBUG - SUCCESS
2025-05-29 04:24:44,667 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 04:24:44,667 - DEBUG - ret[code] = None, after post request
2025-05-29 04:24:44,667 - DEBUG - clear_cache() called
2025-05-29 04:24:44,667 - DEBUG - Session is closed

============================================================       
❌ DEPLOYMENT FAILED
============================================================       

Please check the logs above for error details.
Common issues:
- Incorrect Snowflake credentials
- Insufficient privileges
- Network connectivity issues