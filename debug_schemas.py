#!/usr/bin/env python3
"""
Debug script to check schema and table creation
"""

import snowflake.connector
import json
import getpass

def main():
    # Load config
    with open('config/snowflake_config.json', 'r') as f:
        config = json.load(f)
    
    password = getpass.getpass(f"Enter password for {config['user']}: ")
    
    conn = snowflake.connector.connect(
        account=config['account'],
        user=config['user'],
        password=password,
        role=config['role'],
        warehouse=config['warehouse'],
        database=config['database']
    )
    
    cursor = conn.cursor()
    
    print("=== CHECKING SCHEMAS ===")
    cursor.execute("SHOW SCHEMAS IN DATABASE salesgenie_ai")
    schemas = cursor.fetchall()
    for schema in schemas:
        print(f"Schema: {schema[1]}")
    
    print("\n=== CHECKING ANALYTICS SCHEMA ===")
    try:
        cursor.execute("USE SCHEMA salesgenie_ai.analytics")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        if tables:
            for table in tables:
                print(f"Analytics Table: {table[1]}")
        else:
            print("No tables found in analytics schema")
    except Exception as e:
        print(f"Analytics error: {e}")
    
    print("\n=== CHECKING AI_SERVICES SCHEMA ===")
    try:
        cursor.execute("USE SCHEMA salesgenie_ai.ai_services")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        if tables:
            for table in tables:
                print(f"AI_Services Table: {table[1]}")
        else:
            print("No tables found in ai_services schema")
    except Exception as e:
        print(f"AI_Services error: {e}")
    
    print("\n=== CHECKING INTEGRATION SCHEMA ===")
    try:
        cursor.execute("USE SCHEMA salesgenie_ai.integration")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        if tables:
            for table in tables:
                print(f"Integration Table: {table[1]}")
        else:
            print("No tables found in integration schema")
    except Exception as e:
        print(f"Integration error: {e}")
    
    print("\n=== CHECKING CRM_DATA SCHEMA ===")
    try:
        cursor.execute("USE SCHEMA salesgenie_ai.crm_data")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        if tables:
            for table in tables:
                print(f"CRM_Data Table: {table[1]}")
        else:
            print("No tables found in crm_data schema")
    except Exception as e:
        print(f"CRM_Data error: {e}")
    
    conn.close()

if __name__ == "__main__":
    main()
