#!/usr/bin/env python3
"""
Fix PyArrow version compatibility issue
"""

import subprocess
import sys
import pkg_resources

def check_current_version():
    """Check current PyArrow version"""
    try:
        version = pkg_resources.get_distribution("pyarrow").version
        print(f"Current PyArrow version: {version}")
        return version
    except pkg_resources.DistributionNotFound:
        print("PyArrow not installed")
        return None

def fix_pyarrow():
    """Fix PyArrow version to be compatible with Snowflake"""
    print("🔧 Fixing PyArrow version compatibility...")
    
    current_version = check_current_version()
    
    if current_version:
        major_version = int(current_version.split('.')[0])
        if major_version >= 19:
            print(f"⚠️ PyArrow version {current_version} is incompatible with Snowflake connector")
            print("📦 Installing compatible PyArrow version...")
            
            try:
                # Uninstall current version
                subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "pyarrow", "-y"])
                
                # Install compatible version
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyarrow>=10.0.0,<19.0.0"])
                
                # Verify installation
                new_version = check_current_version()
                print(f"✅ PyArrow updated to version: {new_version}")
                return True
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Error updating PyArrow: {e}")
                return False
        else:
            print(f"✅ PyArrow version {current_version} is compatible")
            return True
    else:
        print("📦 Installing PyArrow...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyarrow>=10.0.0,<19.0.0"])
            new_version = check_current_version()
            print(f"✅ PyArrow installed: {new_version}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Error installing PyArrow: {e}")
            return False

def main():
    """Main function"""
    print("="*60)
    print("🔧 PYARROW COMPATIBILITY FIX")
    print("="*60)
    
    success = fix_pyarrow()
    
    if success:
        print("\n" + "="*60)
        print("✅ PYARROW FIX SUCCESSFUL!")
        print("="*60)
        print("\n🎉 PyArrow is now compatible with Snowflake connector!")
        print("✅ The warning should no longer appear")
        print("\nNext steps:")
        print("1. Run 'python enable_cortex.py' to test Cortex")
        print("2. Run 'streamlit run streamlit_app.py' to start the app")
    else:
        print("\n" + "="*60)
        print("❌ PYARROW FIX FAILED")
        print("="*60)
        print("\n🔧 Manual fix:")
        print("1. Run: pip uninstall pyarrow -y")
        print("2. Run: pip install 'pyarrow>=10.0.0,<19.0.0'")
        sys.exit(1)

if __name__ == "__main__":
    main()
