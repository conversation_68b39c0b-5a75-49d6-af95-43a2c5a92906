# SalesGenie AI - Semantic Model for Cortex Analyst
# Nihilent x Snowflake Hackathon 2025
# 
# This semantic model defines the business logic and relationships
# for conversational AI interactions with sales CRM data

name: "SalesGenie AI CRM Analytics"
description: "Comprehensive semantic model for sales CRM data analysis and conversational AI interactions"

tables:
  # Companies/Accounts table
  - name: companies
    description: "Customer companies and prospects in the sales pipeline"
    base_table:
      database: salesgenie_ai
      schema: crm_data
      table: companies
    dimensions:
      - name: company_name
        expr: company_name
        description: "Name of the company or organization"
        data_type: varchar
        unique: true
        sample_values:
          - "TechCorp Solutions"
          - "Global Manufacturing Inc"
          - "HealthTech Innovations"
          - "Financial Services Group"
          - "Retail Chain Corp"
      - name: industry
        expr: industry
        description: "Industry sector or vertical market"
        data_type: varchar
        sample_values:
          - "Technology"
          - "Manufacturing"
          - "Healthcare"
          - "Financial Services"
          - "Retail"
          - "Education"
          - "Energy"
          - "Transportation"
          - "Media"
          - "Consulting"
      - name: company_size
        expr: company_size
        description: "Company size category based on employee count"
        data_type: varchar
        sample_values:
          - "Small (50-99)"
          - "Medium (100-499)"
          - "Large (500-999)"
          - "Enterprise (1000+)"
      - name: country
        expr: country
        description: "Country where the company is located"
        data_type: varchar
        sample_values:
          - "USA"
          - "Canada"
          - "United Kingdom"
          - "Germany"
          - "France"
      - name: state
        expr: state
        description: "State or province where the company is located"
        data_type: varchar
        sample_values:
          - "CA"
          - "NY"
          - "TX"
          - "FL"
          - "IL"
    measures:
      - name: annual_revenue
        expr: annual_revenue
        description: "Annual revenue of the company in USD"
        data_type: number
        default_aggregation: avg
      - name: company_count
        expr: COUNT(*)
        description: "Total number of companies"
        data_type: number
        default_aggregation: sum
    time_dimensions:
      - name: created_date
        expr: created_date
        description: "Date when the company record was created"
        data_type: timestamp

  # Opportunities table
  - name: opportunities
    description: "Sales opportunities and deals in the pipeline"
    base_table:
      database: salesgenie_ai
      schema: crm_data
      table: opportunities
    dimensions:
      - name: opportunity_name
        expr: opportunity_name
        description: "Name or title of the sales opportunity"
        data_type: varchar
      - name: stage
        expr: stage
        description: "Current stage of the opportunity in the sales process"
        data_type: varchar
        sample_values:
          - "Discovery"
          - "Qualification"
          - "Proposal"
          - "Negotiation"
          - "Closed Won"
          - "Closed Lost"
      - name: sales_rep
        expr: sales_rep
        description: "Sales representative assigned to the opportunity"
        data_type: varchar
      - name: product_category
        expr: product_category
        description: "Primary product category for the opportunity"
        data_type: varchar
        sample_values:
          - "Software"
          - "Services"
          - "Hardware"
      - name: competitor
        expr: competitor
        description: "Main competitor in the deal"
        data_type: varchar
      - name: loss_reason
        expr: loss_reason
        description: "Reason for losing the opportunity"
        data_type: varchar
    measures:
      - name: opportunity_amount
        expr: amount
        description: "Total value of the opportunity in USD"
        data_type: number
        default_aggregation: sum
        synonyms: ["deal value", "opportunity value", "revenue", "sales amount"]
      - name: probability
        expr: probability
        description: "Probability of winning the opportunity (0-100%)"
        data_type: number
        default_aggregation: avg
      - name: weighted_amount
        expr: amount * probability / 100
        description: "Opportunity amount weighted by probability"
        data_type: number
        default_aggregation: sum
      - name: opportunity_count
        expr: COUNT(*)
        description: "Total number of opportunities"
        data_type: number
        default_aggregation: sum
      - name: won_opportunities
        expr: COUNT(CASE WHEN is_won = TRUE THEN 1 END)
        description: "Number of won opportunities"
        data_type: number
        default_aggregation: sum
      - name: lost_opportunities
        expr: COUNT(CASE WHEN is_lost = TRUE THEN 1 END)
        description: "Number of lost opportunities"
        data_type: number
        default_aggregation: sum
      - name: win_rate
        expr: COUNT(CASE WHEN is_won = TRUE THEN 1 END) * 100.0 / COUNT(*)
        description: "Win rate percentage"
        data_type: number
        default_aggregation: avg
    time_dimensions:
      - name: expected_close_date
        expr: expected_close_date
        description: "Expected date to close the opportunity"
        data_type: date
      - name: actual_close_date
        expr: actual_close_date
        description: "Actual date the opportunity was closed"
        data_type: date
      - name: created_date
        expr: created_date
        description: "Date when the opportunity was created"
        data_type: timestamp

  # Leads table
  - name: leads
    description: "Sales leads and prospects not yet converted to opportunities"
    base_table:
      database: salesgenie_ai
      schema: crm_data
      table: leads
    dimensions:
      - name: lead_source
        expr: lead_source
        description: "Source where the lead originated"
        data_type: varchar
        sample_values:
          - "Website"
          - "Trade Show"
          - "Referral"
          - "Cold Call"
          - "LinkedIn"
          - "Email Campaign"
          - "Partner"
          - "Advertisement"
      - name: lead_status
        expr: lead_status
        description: "Current status of the lead"
        data_type: varchar
        sample_values:
          - "New"
          - "Contacted"
          - "Qualified"
          - "Unqualified"
          - "Converted"
      - name: qualification_level
        expr: qualification_level
        description: "Qualification level of the lead"
        data_type: varchar
        sample_values:
          - "Hot"
          - "Warm"
          - "Cold"
      - name: assigned_to
        expr: assigned_to
        description: "Sales representative assigned to the lead"
        data_type: varchar
    measures:
      - name: lead_score
        expr: lead_score
        description: "Lead scoring value (0-100)"
        data_type: number
        default_aggregation: avg
      - name: estimated_value
        expr: estimated_value
        description: "Estimated value of the lead if converted"
        data_type: number
        default_aggregation: sum
      - name: lead_count
        expr: COUNT(*)
        description: "Total number of leads"
        data_type: number
        default_aggregation: sum
      - name: converted_leads
        expr: COUNT(CASE WHEN is_converted = TRUE THEN 1 END)
        description: "Number of converted leads"
        data_type: number
        default_aggregation: sum
      - name: conversion_rate
        expr: COUNT(CASE WHEN is_converted = TRUE THEN 1 END) * 100.0 / COUNT(*)
        description: "Lead conversion rate percentage"
        data_type: number
        default_aggregation: avg
    time_dimensions:
      - name: created_date
        expr: created_date
        description: "Date when the lead was created"
        data_type: timestamp
      - name: last_contacted_date
        expr: last_contacted_date
        description: "Date when the lead was last contacted"
        data_type: timestamp
      - name: next_follow_up_date
        expr: next_follow_up_date
        description: "Scheduled date for next follow-up"
        data_type: timestamp
      - name: estimated_close_date
        expr: estimated_close_date
        description: "Estimated date to close the lead"
        data_type: date

  # Activities table
  - name: activities
    description: "Sales activities including calls, emails, meetings, and tasks"
    base_table:
      database: salesgenie_ai
      schema: crm_data
      table: activities
    dimensions:
      - name: activity_type
        expr: activity_type
        description: "Type of sales activity"
        data_type: varchar
        sample_values:
          - "call"
          - "email"
          - "meeting"
          - "task"
          - "note"
      - name: subject
        expr: subject
        description: "Subject or title of the activity"
        data_type: varchar
      - name: assigned_to
        expr: assigned_to
        description: "Person assigned to the activity"
        data_type: varchar
      - name: completed
        expr: completed
        description: "Whether the activity is completed"
        data_type: boolean
    measures:
      - name: duration_minutes
        expr: duration_minutes
        description: "Duration of the activity in minutes"
        data_type: number
        default_aggregation: sum
      - name: activity_count
        expr: COUNT(*)
        description: "Total number of activities"
        data_type: number
        default_aggregation: sum
      - name: completed_activities
        expr: COUNT(CASE WHEN completed = TRUE THEN 1 END)
        description: "Number of completed activities"
        data_type: number
        default_aggregation: sum
      - name: completion_rate
        expr: COUNT(CASE WHEN completed = TRUE THEN 1 END) * 100.0 / COUNT(*)
        description: "Activity completion rate percentage"
        data_type: number
        default_aggregation: avg
    time_dimensions:
      - name: activity_date
        expr: activity_date
        description: "Date and time when the activity occurred or is scheduled"
        data_type: timestamp
      - name: created_date
        expr: created_date
        description: "Date when the activity record was created"
        data_type: timestamp

# Relationships between tables
relationships:
  - name: opportunities_to_companies
    left_table: opportunities
    right_table: companies
    relationship_columns:
      - left_column: company_id
        right_column: company_id
    join_type: left_outer
    relationship_type: many_to_one

  - name: leads_to_companies
    left_table: leads
    right_table: companies
    relationship_columns:
      - left_column: company_id
        right_column: company_id
    join_type: left_outer
    relationship_type: many_to_one

  - name: activities_to_companies
    left_table: activities
    right_table: companies
    relationship_columns:
      - left_column: company_id
        right_column: company_id
    join_type: left_outer
    relationship_type: many_to_one

  - name: activities_to_opportunities
    left_table: activities
    right_table: opportunities
    relationship_columns:
      - left_column: opportunity_id
        right_column: opportunity_id
    join_type: left_outer
    relationship_type: many_to_one

  - name: activities_to_leads
    left_table: activities
    right_table: leads
    relationship_columns:
      - left_column: lead_id
        right_column: lead_id
    join_type: left_outer
    relationship_type: many_to_one

# Verified queries for improved accuracy
verified_queries:
  - name: "pipeline_value_by_stage"
    question: "What is the total pipeline value by stage?"
    sql: "SELECT stage, SUM(amount) as total_value FROM opportunities WHERE is_won = FALSE AND is_lost = FALSE GROUP BY stage ORDER BY total_value DESC"
    verified_at: 1704067200
    verified_by: "SalesGenie AI"

  - name: "top_opportunities_this_quarter"
    question: "Show me the top 10 opportunities by value closing this quarter"
    sql: "SELECT opportunity_name, amount, expected_close_date, stage FROM opportunities WHERE expected_close_date BETWEEN DATE_TRUNC('quarter', CURRENT_DATE()) AND LAST_DAY(DATE_TRUNC('quarter', CURRENT_DATE())) AND is_won = FALSE AND is_lost = FALSE ORDER BY amount DESC LIMIT 10"
    verified_at: 1704067200
    verified_by: "SalesGenie AI"

  - name: "lead_conversion_by_source"
    question: "What is the lead conversion rate by source?"
    sql: "SELECT lead_source, COUNT(*) as total_leads, COUNT(CASE WHEN is_converted = TRUE THEN 1 END) as converted_leads, ROUND(COUNT(CASE WHEN is_converted = TRUE THEN 1 END) * 100.0 / COUNT(*), 2) as conversion_rate FROM leads GROUP BY lead_source ORDER BY conversion_rate DESC"
    verified_at: 1704067200
    verified_by: "SalesGenie AI"

  - name: "sales_rep_performance"
    question: "Show me sales rep performance including win rates and total revenue"
    sql: "SELECT sales_rep, COUNT(*) as total_opps, COUNT(CASE WHEN is_won = TRUE THEN 1 END) as won_opps, SUM(CASE WHEN is_won = TRUE THEN amount ELSE 0 END) as total_revenue, ROUND(COUNT(CASE WHEN is_won = TRUE THEN 1 END) * 100.0 / COUNT(*), 2) as win_rate FROM opportunities GROUP BY sales_rep ORDER BY total_revenue DESC"
    verified_at: 1704067200
    verified_by: "SalesGenie AI"

  - name: "overdue_follow_ups"
    question: "Which leads have overdue follow-ups?"
    sql: "SELECT l.lead_id, c.company_name, l.next_follow_up_date, l.assigned_to, DATEDIFF('day', l.next_follow_up_date, CURRENT_DATE()) as days_overdue FROM leads l JOIN companies c ON l.company_id = c.company_id WHERE l.next_follow_up_date < CURRENT_DATE() AND l.is_converted = FALSE ORDER BY days_overdue DESC"
    verified_at: 1704067200
    verified_by: "SalesGenie AI"
