/*
SalesGenie AI - AI Services Setup
Nihilent x Snowflake Hackathon 2025

This script sets up Snowflake Cortex AI services including:
- Cortex Search for intelligent document/lead retrieval
- Semantic model deployment
- AI-powered views and functions
*/

USE ROLE salesgenie_ai_role;
USE WAREHOUSE salesgenie_ai_wh;
USE DATABASE salesgenie_ai;

-- =====================================================
-- AI SERVICES SCHEMA SETUP
-- =====================================================

USE SCHEMA salesgenie_ai.ai_services;

-- Create stage for semantic models
CREATE OR REPLACE STAGE semantic_models
    DIRECTORY = (ENABLE = TRUE)
    COMMENT = 'Stage for Cortex Analyst semantic models';

-- =====================================================
-- CORTEX SEARCH SERVICES
-- =====================================================

-- Create search service for companies
CREATE OR REPLACE CORTEX SEARCH SERVICE company_search_service
ON company_search_data
WAREHOUSE = salesgenie_ai_wh
TARGET_LAG = '1 hour'
AS (
    SELECT 
        company_id,
        company_name,
        industry,
        company_size,
        CONCAT(company_name, ' ', industry, ' ', company_size, ' ', COALESCE(state, ''), ' ', COALESCE(country, '')) AS search_text
    FROM salesgenie_ai.crm_data.companies
    WHERE is_active = TRUE
);

-- Create search service for leads
CREATE OR REPLACE CORTEX SEARCH SERVICE lead_search_service
ON lead_search_data  
WAREHOUSE = salesgenie_ai_wh
TARGET_LAG = '1 hour'
AS (
    SELECT 
        l.lead_id,
        c.company_name,
        l.lead_source,
        l.lead_status,
        l.qualification_level,
        l.notes,
        CONCAT(c.company_name, ' ', l.lead_source, ' ', l.lead_status, ' ', l.qualification_level, ' ', COALESCE(l.notes, '')) AS search_text
    FROM salesgenie_ai.crm_data.leads l
    JOIN salesgenie_ai.crm_data.companies c ON l.company_id = c.company_id
    WHERE l.is_converted = FALSE
);

-- Create search service for opportunities
CREATE OR REPLACE CORTEX SEARCH SERVICE opportunity_search_service
ON opportunity_search_data
WAREHOUSE = salesgenie_ai_wh
TARGET_LAG = '1 hour'
AS (
    SELECT 
        o.opportunity_id,
        o.opportunity_name,
        c.company_name,
        o.stage,
        o.product_category,
        CONCAT(o.opportunity_name, ' ', c.company_name, ' ', o.stage, ' ', o.product_category) AS search_text
    FROM salesgenie_ai.crm_data.opportunities o
    JOIN salesgenie_ai.crm_data.companies c ON o.company_id = c.company_id
    WHERE o.is_won = FALSE AND o.is_lost = FALSE
);

-- Create search service for activities
CREATE OR REPLACE CORTEX SEARCH SERVICE activity_search_service
ON activity_search_data
WAREHOUSE = salesgenie_ai_wh
TARGET_LAG = '1 hour'
AS (
    SELECT 
        a.activity_id,
        a.activity_type,
        a.subject,
        a.description,
        c.company_name,
        CONCAT(a.activity_type, ' ', COALESCE(a.subject, ''), ' ', COALESCE(a.description, ''), ' ', COALESCE(c.company_name, '')) AS search_text
    FROM salesgenie_ai.crm_data.activities a
    LEFT JOIN salesgenie_ai.crm_data.companies c ON a.company_id = c.company_id
);

-- =====================================================
-- AI-POWERED FUNCTIONS
-- =====================================================

-- Function to score leads using AI
CREATE OR REPLACE FUNCTION ai_lead_scoring(
    company_revenue FLOAT,
    industry VARCHAR,
    company_size VARCHAR,
    lead_source VARCHAR,
    contact_title VARCHAR
)
RETURNS FLOAT
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
PACKAGES = ('snowflake-snowpark-python')
HANDLER = 'score_lead'
AS
$$
def score_lead(company_revenue, industry, company_size, lead_source, contact_title):
    """
    AI-powered lead scoring algorithm
    Returns a score between 0-100
    """
    score = 50  # Base score
    
    # Revenue scoring
    if company_revenue:
        if company_revenue > 50000000:
            score += 20
        elif company_revenue > 10000000:
            score += 15
        elif company_revenue > 1000000:
            score += 10
        else:
            score += 5
    
    # Industry scoring
    high_value_industries = ['Technology', 'Financial Services', 'Healthcare']
    if industry in high_value_industries:
        score += 15
    
    # Company size scoring
    if company_size == 'Enterprise (1000+)':
        score += 15
    elif company_size == 'Large (500-999)':
        score += 10
    elif company_size == 'Medium (100-499)':
        score += 5
    
    # Lead source scoring
    high_quality_sources = ['Referral', 'Website', 'Partner']
    if lead_source in high_quality_sources:
        score += 10
    
    # Contact title scoring
    decision_maker_titles = ['CEO', 'CTO', 'CFO', 'VP', 'Director', 'Chief']
    if contact_title and any(title in contact_title for title in decision_maker_titles):
        score += 10
    
    return min(100, max(0, score))
$$;

-- Function to generate AI-powered email templates
CREATE OR REPLACE FUNCTION generate_email_template(
    contact_name VARCHAR,
    company_name VARCHAR,
    industry VARCHAR,
    email_type VARCHAR
)
RETURNS VARCHAR
LANGUAGE SQL
AS
$$
    SELECT SNOWFLAKE.CORTEX.COMPLETE(
        'llama3.1-70b',
        CONCAT(
            'Generate a professional sales email for the following context:\n',
            'Contact: ', contact_name, '\n',
            'Company: ', company_name, '\n', 
            'Industry: ', industry, '\n',
            'Email Type: ', email_type, '\n\n',
            'Requirements:\n',
            '- Professional and personalized tone\n',
            '- Industry-specific value proposition\n',
            '- Clear call-to-action\n',
            '- Maximum 200 words\n',
            '- Include subject line\n\n',
            'Email:'
        )
    )
$$;

-- Function to analyze opportunity risk
CREATE OR REPLACE FUNCTION analyze_opportunity_risk(
    stage VARCHAR,
    probability FLOAT,
    days_in_pipeline INT,
    last_activity_days INT,
    amount FLOAT
)
RETURNS VARCHAR
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
HANDLER = 'assess_risk'
AS
$$
def assess_risk(stage, probability, days_in_pipeline, last_activity_days, amount):
    """
    Analyze opportunity risk and provide recommendations
    """
    risk_score = 0
    risk_factors = []
    
    # Stage-based risk
    if stage in ['Discovery', 'Qualification'] and days_in_pipeline > 60:
        risk_score += 30
        risk_factors.append('Long time in early stage')
    elif stage in ['Proposal', 'Negotiation'] and days_in_pipeline > 90:
        risk_score += 40
        risk_factors.append('Extended negotiation period')
    
    # Probability risk
    if probability < 30:
        risk_score += 25
        risk_factors.append('Low win probability')
    
    # Activity risk
    if last_activity_days > 14:
        risk_score += 20
        risk_factors.append('No recent activity')
    elif last_activity_days > 7:
        risk_score += 10
        risk_factors.append('Limited recent engagement')
    
    # Amount-based priority
    if amount > 100000:
        risk_score += 5  # High-value deals get extra attention
    
    # Determine risk level
    if risk_score >= 60:
        risk_level = 'HIGH'
    elif risk_score >= 30:
        risk_level = 'MEDIUM'
    else:
        risk_level = 'LOW'
    
    return f"{risk_level} RISK ({risk_score}): {', '.join(risk_factors) if risk_factors else 'No major risk factors'}"
$$;

-- =====================================================
-- AI-ENHANCED VIEWS
-- =====================================================

-- Enhanced pipeline view with AI insights
CREATE OR REPLACE VIEW ai_enhanced_pipeline AS
SELECT 
    o.opportunity_id,
    o.opportunity_name,
    c.company_name,
    c.industry,
    c.annual_revenue,
    o.stage,
    o.probability,
    o.amount,
    o.expected_close_date,
    o.sales_rep,
    DATEDIFF('day', o.created_date, CURRENT_DATE()) AS days_in_pipeline,
    COALESCE(DATEDIFF('day', last_activity.activity_date, CURRENT_DATE()), 999) AS days_since_last_activity,
    
    -- AI-powered risk assessment
    analyze_opportunity_risk(
        o.stage, 
        o.probability, 
        DATEDIFF('day', o.created_date, CURRENT_DATE()),
        COALESCE(DATEDIFF('day', last_activity.activity_date, CURRENT_DATE()), 999),
        o.amount
    ) AS ai_risk_assessment,
    
    -- Urgency classification
    CASE 
        WHEN o.expected_close_date <= CURRENT_DATE() THEN 'OVERDUE'
        WHEN o.expected_close_date <= DATEADD('day', 7, CURRENT_DATE()) THEN 'URGENT'
        WHEN o.expected_close_date <= DATEADD('day', 30, CURRENT_DATE()) THEN 'SOON'
        ELSE 'FUTURE'
    END AS urgency_level,
    
    -- AI sentiment from recent activities
    COALESCE(
        SNOWFLAKE.CORTEX.SENTIMENT(last_activity.description),
        0
    ) AS recent_activity_sentiment

FROM salesgenie_ai.crm_data.opportunities o
JOIN salesgenie_ai.crm_data.companies c ON o.company_id = c.company_id
LEFT JOIN (
    SELECT 
        opportunity_id,
        activity_date,
        description,
        ROW_NUMBER() OVER (PARTITION BY opportunity_id ORDER BY activity_date DESC) as rn
    FROM salesgenie_ai.crm_data.activities 
    WHERE opportunity_id IS NOT NULL
) last_activity ON o.opportunity_id = last_activity.opportunity_id AND last_activity.rn = 1
WHERE o.is_won = FALSE AND o.is_lost = FALSE;

-- Enhanced lead view with AI scoring
CREATE OR REPLACE VIEW ai_enhanced_leads AS
SELECT 
    l.lead_id,
    c.company_name,
    c.industry,
    c.company_size,
    c.annual_revenue,
    cont.first_name,
    cont.last_name,
    cont.job_title,
    cont.decision_maker,
    l.lead_source,
    l.lead_status,
    l.qualification_level,
    l.lead_score AS original_lead_score,
    
    -- AI-enhanced lead scoring
    ai_lead_scoring(
        c.annual_revenue,
        c.industry,
        c.company_size,
        l.lead_source,
        cont.job_title
    ) AS ai_lead_score,
    
    l.estimated_value,
    l.estimated_close_date,
    l.assigned_to,
    DATEDIFF('day', l.last_contacted_date, CURRENT_DATE()) AS days_since_contact,
    
    -- Follow-up urgency
    CASE 
        WHEN l.next_follow_up_date < CURRENT_DATE() THEN 'OVERDUE'
        WHEN l.next_follow_up_date <= DATEADD('day', 3, CURRENT_DATE()) THEN 'URGENT'
        WHEN l.next_follow_up_date <= DATEADD('day', 7, CURRENT_DATE()) THEN 'SOON'
        ELSE 'SCHEDULED'
    END AS follow_up_urgency,
    
    -- AI sentiment from notes
    COALESCE(
        SNOWFLAKE.CORTEX.SENTIMENT(l.notes),
        0
    ) AS notes_sentiment

FROM salesgenie_ai.crm_data.leads l
JOIN salesgenie_ai.crm_data.companies c ON l.company_id = c.company_id
LEFT JOIN salesgenie_ai.crm_data.contacts cont ON l.contact_id = cont.contact_id
WHERE l.is_converted = FALSE;

-- Sales rep performance with AI insights
CREATE OR REPLACE VIEW ai_sales_performance AS
SELECT 
    u.user_id,
    CONCAT(u.first_name, ' ', u.last_name) AS sales_rep_name,
    u.territory,
    
    -- Opportunity metrics
    COUNT(DISTINCT o.opportunity_id) AS total_opportunities,
    COUNT(DISTINCT CASE WHEN o.is_won = TRUE THEN o.opportunity_id END) AS won_opportunities,
    COUNT(DISTINCT CASE WHEN o.is_lost = TRUE THEN o.opportunity_id END) AS lost_opportunities,
    SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) AS total_revenue,
    AVG(CASE WHEN o.is_won = TRUE THEN o.amount END) AS avg_deal_size,
    ROUND(COUNT(DISTINCT CASE WHEN o.is_won = TRUE THEN o.opportunity_id END) * 100.0 / 
          NULLIF(COUNT(DISTINCT o.opportunity_id), 0), 2) AS win_rate_percent,
    
    -- Lead metrics
    COUNT(DISTINCT l.lead_id) AS total_leads,
    COUNT(DISTINCT CASE WHEN l.is_converted = TRUE THEN l.lead_id END) AS converted_leads,
    ROUND(COUNT(DISTINCT CASE WHEN l.is_converted = TRUE THEN l.lead_id END) * 100.0 / 
          NULLIF(COUNT(DISTINCT l.lead_id), 0), 2) AS lead_conversion_rate,
    
    -- Activity metrics
    COUNT(DISTINCT a.activity_id) AS total_activities,
    COUNT(DISTINCT CASE WHEN a.completed = TRUE THEN a.activity_id END) AS completed_activities,
    
    -- AI performance insights
    AVG(COALESCE(SNOWFLAKE.CORTEX.SENTIMENT(a.description), 0)) AS avg_activity_sentiment,
    
    -- Performance classification
    CASE 
        WHEN COUNT(DISTINCT CASE WHEN o.is_won = TRUE THEN o.opportunity_id END) * 100.0 / 
             NULLIF(COUNT(DISTINCT o.opportunity_id), 0) >= 25 THEN 'TOP_PERFORMER'
        WHEN COUNT(DISTINCT CASE WHEN o.is_won = TRUE THEN o.opportunity_id END) * 100.0 / 
             NULLIF(COUNT(DISTINCT o.opportunity_id), 0) >= 15 THEN 'GOOD_PERFORMER'
        WHEN COUNT(DISTINCT CASE WHEN o.is_won = TRUE THEN o.opportunity_id END) * 100.0 / 
             NULLIF(COUNT(DISTINCT o.opportunity_id), 0) >= 5 THEN 'AVERAGE_PERFORMER'
        ELSE 'NEEDS_SUPPORT'
    END AS performance_tier

FROM salesgenie_ai.crm_data.sales_users u
LEFT JOIN salesgenie_ai.crm_data.opportunities o ON u.user_id = o.sales_rep
LEFT JOIN salesgenie_ai.crm_data.leads l ON u.user_id = l.assigned_to
LEFT JOIN salesgenie_ai.crm_data.activities a ON u.user_id = a.assigned_to
WHERE u.role = 'sales_rep'
GROUP BY u.user_id, u.first_name, u.last_name, u.territory;

COMMIT;

-- =====================================================
-- SAMPLE AI QUERIES FOR TESTING
-- =====================================================

-- Test AI lead scoring
SELECT 
    company_name,
    original_lead_score,
    ai_lead_score,
    ai_lead_score - original_lead_score AS score_difference
FROM ai_enhanced_leads
ORDER BY ai_lead_score DESC;

-- Test opportunity risk assessment
SELECT 
    opportunity_name,
    company_name,
    stage,
    amount,
    ai_risk_assessment,
    urgency_level
FROM ai_enhanced_pipeline
ORDER BY 
    CASE urgency_level 
        WHEN 'OVERDUE' THEN 1
        WHEN 'URGENT' THEN 2
        WHEN 'SOON' THEN 3
        ELSE 4
    END,
    amount DESC;

-- Test sales performance insights
SELECT 
    sales_rep_name,
    territory,
    total_opportunities,
    win_rate_percent,
    total_revenue,
    performance_tier,
    avg_activity_sentiment
FROM ai_sales_performance
ORDER BY total_revenue DESC;
