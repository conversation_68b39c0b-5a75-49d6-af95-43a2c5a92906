#!/usr/bin/env python3
"""
Update Snowflake password in config file
"""

import json
import getpass
import os

def update_password():
    """Update password in config file"""
    config_path = "config/snowflake_config.json"
    
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {
            "account": "GNBXJLF-PKB97538",
            "user": "ASHUTOSH1",
            "warehouse": "SALESGENIE_AI_WH",
            "database": "SALESGENIE_AI",
            "schema": "CRM_DATA",
            "role": "ACCOUNTADMIN"
        }
    
    print("Current config:")
    print(f"Account: {config.get('account')}")
    print(f"User: {config.get('user')}")
    print(f"Password: {'*' * len(config.get('password', '')) if config.get('password') else 'Not set'}")
    
    # Get new password
    password = getpass.getpass("Enter your Snowflake password: ")
    
    # Update config
    config["password"] = password
    
    # Save config
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)
    
    print("✅ Password updated successfully!")
    print("You can now run the Streamlit app with: streamlit run streamlit_app.py")

if __name__ == "__main__":
    update_password()
