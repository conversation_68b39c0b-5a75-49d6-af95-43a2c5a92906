/*
SalesGenie AI - Integration Schema Setup
Nihilent x Snowflake Hackathon 2025

This script creates the integration schema with external system connectors,
data staging tables, and ETL processes for CRM integrations.
*/

USE ROLE ACCOUNTADMIN;
USE WAREHOUSE salesgenie_ai_wh;
USE DATABASE salesgenie_ai;
USE SCHEMA salesgenie_ai.integration;

-- =====================================================
-- INTEGRATION SCHEMA SETUP
-- =====================================================

-- External system configurations
CREATE OR REPLACE TABLE external_systems (
    system_id STRING PRIMARY KEY,
    system_name STRING NOT NULL,
    system_type STRING, -- 'CRM', 'ERP', 'Marketing', 'Support'
    api_endpoint STRING,
    authentication_type STRING, -- 'oauth', 'api_key', 'basic_auth'
    connection_status STRING DEFAULT 'inactive', -- 'active', 'inactive', 'error'
    last_sync_date TIMESTAMP_NTZ,
    sync_frequency STRING, -- 'hourly', 'daily', 'weekly'
    data_mapping VARIANT,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Data sync logs
CREATE OR REPLACE TABLE sync_logs (
    log_id STRING PRIMARY KEY,
    system_id STRING,
    sync_type STRING, -- 'full', 'incremental'
    sync_status STRING, -- 'started', 'completed', 'failed', 'partial'
    records_processed NUMBER(10,0),
    records_success NUMBER(10,0),
    records_failed NUMBER(10,0),
    error_message STRING,
    sync_start_time TIMESTAMP_NTZ,
    sync_end_time TIMESTAMP_NTZ,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Staging tables for external data
CREATE OR REPLACE TABLE staging_companies (
    staging_id STRING PRIMARY KEY,
    external_id STRING,
    system_id STRING,
    company_name STRING,
    industry STRING,
    company_size STRING,
    annual_revenue NUMBER(15,2),
    website STRING,
    phone STRING,
    address STRING,
    city STRING,
    state STRING,
    country STRING,
    postal_code STRING,
    raw_data VARIANT,
    sync_status STRING DEFAULT 'pending', -- 'pending', 'processed', 'error'
    error_message STRING,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    processed_date TIMESTAMP_NTZ
);

CREATE OR REPLACE TABLE staging_contacts (
    staging_id STRING PRIMARY KEY,
    external_id STRING,
    system_id STRING,
    company_external_id STRING,
    first_name STRING,
    last_name STRING,
    email STRING,
    phone STRING,
    job_title STRING,
    department STRING,
    decision_maker BOOLEAN,
    raw_data VARIANT,
    sync_status STRING DEFAULT 'pending',
    error_message STRING,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    processed_date TIMESTAMP_NTZ
);

CREATE OR REPLACE TABLE staging_opportunities (
    staging_id STRING PRIMARY KEY,
    external_id STRING,
    system_id STRING,
    company_external_id STRING,
    opportunity_name STRING,
    stage STRING,
    amount NUMBER(15,2),
    probability NUMBER(5,2),
    expected_close_date DATE,
    sales_rep_external_id STRING,
    competitor STRING,
    raw_data VARIANT,
    sync_status STRING DEFAULT 'pending',
    error_message STRING,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    processed_date TIMESTAMP_NTZ
);

CREATE OR REPLACE TABLE staging_activities (
    staging_id STRING PRIMARY KEY,
    external_id STRING,
    system_id STRING,
    company_external_id STRING,
    contact_external_id STRING,
    opportunity_external_id STRING,
    activity_type STRING,
    subject STRING,
    description STRING,
    activity_date TIMESTAMP_NTZ,
    assigned_to_external_id STRING,
    completed BOOLEAN,
    raw_data VARIANT,
    sync_status STRING DEFAULT 'pending',
    error_message STRING,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    processed_date TIMESTAMP_NTZ
);

-- Data mapping tables
CREATE OR REPLACE TABLE field_mappings (
    mapping_id STRING PRIMARY KEY,
    system_id STRING,
    source_table STRING,
    source_field STRING,
    target_table STRING,
    target_field STRING,
    transformation_rule STRING,
    is_required BOOLEAN DEFAULT FALSE,
    default_value STRING,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- API rate limiting and quotas
CREATE OR REPLACE TABLE api_quotas (
    quota_id STRING PRIMARY KEY,
    system_id STRING,
    quota_type STRING, -- 'daily', 'hourly', 'monthly'
    quota_limit NUMBER(10,0),
    quota_used NUMBER(10,0),
    quota_reset_time TIMESTAMP_NTZ,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Webhook configurations
CREATE OR REPLACE TABLE webhook_configs (
    webhook_id STRING PRIMARY KEY,
    system_id STRING,
    webhook_url STRING,
    webhook_secret STRING,
    event_types VARIANT, -- Array of event types to listen for
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Real-time event processing
CREATE OR REPLACE TABLE webhook_events (
    event_id STRING PRIMARY KEY,
    webhook_id STRING,
    event_type STRING,
    event_data VARIANT,
    processing_status STRING DEFAULT 'pending', -- 'pending', 'processed', 'failed'
    error_message STRING,
    received_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    processed_date TIMESTAMP_NTZ
);

-- =====================================================
-- SAMPLE INTEGRATION DATA
-- =====================================================

-- Insert sample external systems
INSERT INTO external_systems (system_id, system_name, system_type, api_endpoint, authentication_type, connection_status, sync_frequency, data_mapping) VALUES
('sys_001', 'Salesforce Production', 'CRM', 'https://api.salesforce.com/v1', 'oauth', 'active', 'hourly',
 PARSE_JSON('{"companies": {"Account": "companies"}, "contacts": {"Contact": "contacts"}, "opportunities": {"Opportunity": "opportunities"}}')),
('sys_002', 'HubSpot Marketing', 'CRM', 'https://api.hubapi.com/v3', 'api_key', 'active', 'daily',
 PARSE_JSON('{"companies": {"companies": "companies"}, "contacts": {"contacts": "contacts"}, "deals": {"opportunities": "opportunities"}}')),
('sys_003', 'Pipedrive Sales', 'CRM', 'https://api.pipedrive.com/v1', 'api_key', 'inactive', 'daily',
 PARSE_JSON('{"organizations": {"companies": "companies"}, "persons": {"contacts": "contacts"}, "deals": {"opportunities": "opportunities"}}')),
('sys_004', 'Microsoft Dynamics', 'CRM', 'https://api.dynamics.com/v9.0', 'oauth', 'active', 'hourly',
 PARSE_JSON('{"accounts": {"companies": "companies"}, "contacts": {"contacts": "contacts"}, "opportunities": {"opportunities": "opportunities"}}')),
('sys_005', 'Zendesk Support', 'Support', 'https://api.zendesk.com/v2', 'api_key', 'active', 'daily',
 PARSE_JSON('{"organizations": {"companies": "companies"}, "users": {"contacts": "contacts"}, "tickets": {"activities": "activities"}}'));

-- Insert sync logs
INSERT INTO sync_logs (log_id, system_id, sync_type, sync_status, records_processed, records_success, records_failed, sync_start_time, sync_end_time) VALUES
('log_001', 'sys_001', 'incremental', 'completed', 150, 148, 2, '2025-01-15 08:00:00', '2025-01-15 08:15:00'),
('log_002', 'sys_001', 'incremental', 'completed', 89, 89, 0, '2025-01-15 09:00:00', '2025-01-15 09:08:00'),
('log_003', 'sys_002', 'full', 'completed', 2500, 2485, 15, '2025-01-15 02:00:00', '2025-01-15 02:45:00'),
('log_004', 'sys_004', 'incremental', 'failed', 0, 0, 0, '2025-01-15 10:00:00', '2025-01-15 10:01:00'),
('log_005', 'sys_005', 'incremental', 'completed', 45, 45, 0, '2025-01-15 11:00:00', '2025-01-15 11:05:00');

-- Insert staging data samples
INSERT INTO staging_companies (staging_id, external_id, system_id, company_name, industry, company_size, annual_revenue, website, sync_status) VALUES
('stg_comp_001', 'sf_001234', 'sys_001', 'Enterprise Tech Solutions', 'Technology', 'Large', 50000000.00, 'https://enterprisetech.com', 'processed'),
('stg_comp_002', 'hub_567890', 'sys_002', 'Global Manufacturing Corp', 'Manufacturing', 'Enterprise', *********.00, 'https://globalmfg.com', 'processed'),
('stg_comp_003', 'dyn_111222', 'sys_004', 'HealthCare Innovations', 'Healthcare', 'Medium', 25000000.00, 'https://healthinnovate.com', 'pending'),
('stg_comp_004', 'sf_333444', 'sys_001', 'Financial Services Group', 'Financial Services', 'Large', 75000000.00, 'https://finservices.com', 'processed'),
('stg_comp_005', 'hub_555666', 'sys_002', 'Retail Chain Network', 'Retail', 'Enterprise', *********.00, 'https://retailchain.com', 'error');

COMMIT;
