#!/usr/bin/env python3
"""
SalesGenie AI - Complete Schema Population Script (Pure Python)
Nihilent x Snowflake Hackathon 2025

This script populates all schemas with comprehensive data using pure Python execution.
"""

import os
import sys
import json
import getpass
import snowflake.connector
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_snowflake_connection():
    """Get Snowflake connection with credentials"""

    # Load config
    config_path = "config/snowflake_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {
            "account": "GNBXJLF-PKB97538",
            "user": "ASHUTOSH1",
            "warehouse": "salesgenie_ai_wh",
            "database": "salesgenie_ai",
            "schema": "crm_data",
            "role": "ACCOUNTADMIN"
        }

    # Get password securely
    if config.get("password") == "XXXXXXXXXXXXXXXXXX" or not config.get("password"):
        password = getpass.getpass(f"Enter password for {config['user']}: ")
    else:
        password = config["password"]

    try:
        logger.info("Connecting to Snowflake...")
        connection = snowflake.connector.connect(
            account=config["account"],
            user=config["user"],
            password=password,
            role=config["role"],
            warehouse=config["warehouse"],
            database=config["database"],
            schema=config["schema"]
        )
        logger.info("Successfully connected to Snowflake")
        return connection
    except Exception as e:
        logger.error(f"Failed to connect to Snowflake: {str(e)}")
        return None

def execute_sql_commands(connection, commands, description):
    """Execute a list of SQL commands"""
    cursor = connection.cursor()

    try:
        logger.info(f"Executing {description}...")

        for i, command in enumerate(commands):
            if command.strip():
                try:
                    cursor.execute(command)
                    logger.debug(f"Executed command {i+1}/{len(commands)}")
                except Exception as e:
                    logger.warning(f"Command {i+1} failed: {str(e)}")
                    logger.warning(f"Failed command: {command[:100]}...")

        logger.info(f"✅ Successfully executed {description}")
        return True

    except Exception as e:
        logger.error(f"❌ Error executing {description}: {str(e)}")
        return False
    finally:
        cursor.close()

def create_analytics_schema(connection):
    """Create analytics schema tables and data"""

    commands = [
        "USE ROLE ACCOUNTADMIN",
        "USE WAREHOUSE salesgenie_ai_wh",
        "USE DATABASE salesgenie_ai",
        "USE SCHEMA salesgenie_ai.analytics",

        # Create analytics tables
        """CREATE OR REPLACE TABLE sales_forecasts (
            forecast_id STRING PRIMARY KEY,
            forecast_date DATE,
            forecast_period STRING,
            territory STRING,
            sales_rep STRING,
            predicted_revenue NUMBER(15,2),
            confidence_interval_low NUMBER(15,2),
            confidence_interval_high NUMBER(15,2),
            model_version STRING,
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
        )""",

        """CREATE OR REPLACE TABLE historical_metrics (
            metric_id STRING PRIMARY KEY,
            metric_date DATE,
            metric_type STRING,
            territory STRING,
            sales_rep STRING,
            metric_value NUMBER(10,4),
            benchmark_value NUMBER(10,4),
            variance_percent NUMBER(6,2),
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
        )""",

        """CREATE OR REPLACE TABLE churn_predictions (
            prediction_id STRING PRIMARY KEY,
            company_id STRING,
            churn_probability NUMBER(5,4),
            churn_risk_level STRING,
            key_risk_factors VARIANT,
            recommended_actions VARIANT,
            prediction_date DATE,
            model_confidence NUMBER(5,4),
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
        )""",

        """CREATE OR REPLACE TABLE performance_benchmarks (
            benchmark_id STRING PRIMARY KEY,
            benchmark_type STRING,
            benchmark_category STRING,
            metric_name STRING,
            benchmark_value NUMBER(15,4),
            percentile_25 NUMBER(15,4),
            percentile_50 NUMBER(15,4),
            percentile_75 NUMBER(15,4),
            percentile_90 NUMBER(15,4),
            data_source STRING,
            effective_date DATE,
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
        )""",

        # Insert sample data
        """INSERT INTO sales_forecasts (forecast_id, forecast_date, forecast_period, territory, sales_rep, predicted_revenue, confidence_interval_low, confidence_interval_high, model_version) VALUES
        ('forecast_001', '2025-02-01', 'monthly', 'West Coast', 'user_002', 125000.00, 95000.00, 155000.00, 'v1.2'),
        ('forecast_002', '2025-02-01', 'monthly', 'East Coast', 'user_003', 110000.00, 85000.00, 135000.00, 'v1.2'),
        ('forecast_003', '2025-02-01', 'monthly', 'Central', 'user_004', 95000.00, 70000.00, 120000.00, 'v1.2'),
        ('forecast_004', '2025-03-01', 'monthly', 'West Coast', 'user_002', 135000.00, 105000.00, 165000.00, 'v1.2'),
        ('forecast_005', '2025-03-01', 'monthly', 'East Coast', 'user_003', 120000.00, 95000.00, 145000.00, 'v1.2')""",

        """INSERT INTO historical_metrics (metric_id, metric_date, metric_type, territory, sales_rep, metric_value, benchmark_value, variance_percent) VALUES
        ('metric_001', '2024-12-01', 'conversion_rate', 'West Coast', 'user_002', 0.2250, 0.2000, 12.50),
        ('metric_002', '2024-12-01', 'conversion_rate', 'East Coast', 'user_003', 0.1850, 0.2000, -7.50),
        ('metric_003', '2024-12-01', 'conversion_rate', 'Central', 'user_004', 0.1950, 0.2000, -2.50),
        ('metric_004', '2024-12-01', 'win_rate', 'West Coast', 'user_002', 0.3500, 0.3000, 16.67),
        ('metric_005', '2024-12-01', 'win_rate', 'East Coast', 'user_003', 0.2800, 0.3000, -6.67)""",

        """INSERT INTO churn_predictions (prediction_id, company_id, churn_probability, churn_risk_level, prediction_date, model_confidence) VALUES
        ('churn_001', 'comp_001', 0.1500, 'Low', '2025-01-15', 0.8500),
        ('churn_002', 'comp_002', 0.3500, 'Medium', '2025-01-15', 0.7800),
        ('churn_003', 'comp_003', 0.7200, 'High', '2025-01-15', 0.9200),
        ('churn_004', 'comp_004', 0.2200, 'Low', '2025-01-15', 0.8200)""",

        """INSERT INTO performance_benchmarks (benchmark_id, benchmark_type, benchmark_category, metric_name, benchmark_value, percentile_25, percentile_50, percentile_75, percentile_90, data_source, effective_date) VALUES
        ('bench_001', 'industry', 'Technology', 'lead_conversion_rate', 0.2000, 0.1500, 0.2000, 0.2500, 0.3000, 'Industry Report 2024', '2024-12-01'),
        ('bench_002', 'industry', 'Technology', 'win_rate', 0.3000, 0.2200, 0.3000, 0.3800, 0.4500, 'Industry Report 2024', '2024-12-01'),
        ('bench_003', 'company', 'SalesGenie_AI', 'lead_conversion_rate', 0.2100, 0.1800, 0.2100, 0.2400, 0.2700, 'Internal Analytics', '2024-12-01')""",

        "COMMIT"
    ]

    return execute_sql_commands(connection, commands, "Analytics Schema")

def create_ai_services_schema(connection):
    """Create AI services schema tables and data"""

    commands = [
        "USE ROLE ACCOUNTADMIN",
        "USE WAREHOUSE salesgenie_ai_wh",
        "USE DATABASE salesgenie_ai",
        "USE SCHEMA salesgenie_ai.ai_services",

        # Create AI services tables
        """CREATE OR REPLACE TABLE ai_model_configs (
            config_id STRING PRIMARY KEY,
            model_name STRING NOT NULL,
            model_type STRING,
            model_version STRING,
            configuration VARIANT,
            performance_metrics VARIANT,
            is_active BOOLEAN DEFAULT TRUE,
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
            updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
        )""",

        """CREATE OR REPLACE TABLE cortex_usage_logs (
            usage_id STRING PRIMARY KEY,
            service_type STRING,
            model_name STRING,
            input_tokens NUMBER(10,0),
            output_tokens NUMBER(10,0),
            request_timestamp TIMESTAMP_NTZ,
            response_time_ms NUMBER(10,0),
            user_id STRING,
            session_id STRING,
            cost_estimate NUMBER(10,4),
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
        )""",

        """CREATE OR REPLACE TABLE training_datasets (
            dataset_id STRING PRIMARY KEY,
            dataset_name STRING NOT NULL,
            dataset_type STRING,
            data_source STRING,
            record_count NUMBER(10,0),
            feature_columns VARIANT,
            target_column STRING,
            data_quality_score NUMBER(5,4),
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
            last_updated TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
        )""",

        """CREATE OR REPLACE TABLE ai_recommendations (
            recommendation_id STRING PRIMARY KEY,
            recommendation_type STRING,
            entity_type STRING,
            entity_id STRING,
            recommendation_text STRING,
            confidence_score NUMBER(5,4),
            model_used STRING,
            user_feedback STRING,
            action_taken BOOLEAN DEFAULT FALSE,
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
            feedback_date TIMESTAMP_NTZ
        )""",

        # Insert sample data
        """INSERT INTO ai_model_configs (config_id, model_name, model_type, model_version) VALUES
        ('config_001', 'claude-3-5-sonnet', 'cortex_llm', 'v1.0'),
        ('config_002', 'llama3.1-70b', 'cortex_llm', 'v1.0'),
        ('config_003', 'mistral-large2', 'cortex_llm', 'v1.0'),
        ('config_004', 'sales_crm_analyst', 'cortex_analyst', 'v2.1')""",

        """INSERT INTO cortex_usage_logs (usage_id, service_type, model_name, input_tokens, output_tokens, request_timestamp, response_time_ms, user_id, session_id, cost_estimate) VALUES
        ('usage_001', 'complete', 'claude-3-5-sonnet', 150, 320, '2025-01-15 09:15:00', 1200, 'user_002', 'sess_001', 0.0045),
        ('usage_002', 'complete', 'llama3.1-70b', 89, 245, '2025-01-15 09:18:00', 950, 'user_003', 'sess_002', 0.0028),
        ('usage_003', 'sentiment', 'claude-3-5-sonnet', 45, 12, '2025-01-15 09:22:00', 450, 'user_002', 'sess_001', 0.0008)""",

        """INSERT INTO training_datasets (dataset_id, dataset_name, dataset_type, data_source, record_count, target_column, data_quality_score) VALUES
        ('dataset_001', 'Lead Scoring Training Set Q4 2024', 'lead_scoring', 'salesgenie_ai.crm_data.leads', 5000, 'is_converted', 0.9200),
        ('dataset_002', 'Opportunity Prediction Dataset', 'opportunity_prediction', 'salesgenie_ai.crm_data.opportunities', 3500, 'is_won', 0.8800),
        ('dataset_003', 'Customer Sentiment Analysis', 'sentiment_analysis', 'salesgenie_ai.crm_data.activities', 8000, 'sentiment_score', 0.9100)""",

        """INSERT INTO ai_recommendations (recommendation_id, recommendation_type, entity_type, entity_id, recommendation_text, confidence_score, model_used, user_feedback, action_taken) VALUES
        ('rec_001', 'next_action', 'lead', 'lead_001', 'Schedule demo call within 48 hours - high engagement detected', 0.8500, 'lead_scoring_model', 'helpful', TRUE),
        ('rec_002', 'deal_risk', 'opportunity', 'opp_001', 'Risk of stalling - competitor evaluation in progress, recommend executive meeting', 0.7800, 'opportunity_risk_model', 'helpful', TRUE)""",

        "COMMIT"
    ]

    return execute_sql_commands(connection, commands, "AI Services Schema")

def create_integration_schema(connection):
    """Create integration schema tables and data"""

    commands = [
        "USE ROLE ACCOUNTADMIN",
        "USE WAREHOUSE salesgenie_ai_wh",
        "USE DATABASE salesgenie_ai",
        "USE SCHEMA salesgenie_ai.integration",

        # Create integration tables
        """CREATE OR REPLACE TABLE external_systems (
            system_id STRING PRIMARY KEY,
            system_name STRING NOT NULL,
            system_type STRING,
            api_endpoint STRING,
            authentication_type STRING,
            connection_status STRING DEFAULT 'inactive',
            last_sync_date TIMESTAMP_NTZ,
            sync_frequency STRING,
            data_mapping VARIANT,
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
            updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
            is_active BOOLEAN DEFAULT TRUE
        )""",

        """CREATE OR REPLACE TABLE sync_logs (
            log_id STRING PRIMARY KEY,
            system_id STRING,
            sync_type STRING,
            sync_status STRING,
            records_processed NUMBER(10,0),
            records_success NUMBER(10,0),
            records_failed NUMBER(10,0),
            error_message STRING,
            sync_start_time TIMESTAMP_NTZ,
            sync_end_time TIMESTAMP_NTZ,
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
        )""",

        """CREATE OR REPLACE TABLE staging_companies (
            staging_id STRING PRIMARY KEY,
            external_id STRING,
            system_id STRING,
            company_name STRING,
            industry STRING,
            company_size STRING,
            annual_revenue NUMBER(15,2),
            website STRING,
            phone STRING,
            address STRING,
            city STRING,
            state STRING,
            country STRING,
            postal_code STRING,
            raw_data VARIANT,
            sync_status STRING DEFAULT 'pending',
            error_message STRING,
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
            processed_date TIMESTAMP_NTZ
        )""",

        """CREATE OR REPLACE TABLE staging_contacts (
            staging_id STRING PRIMARY KEY,
            external_id STRING,
            system_id STRING,
            company_external_id STRING,
            first_name STRING,
            last_name STRING,
            email STRING,
            phone STRING,
            job_title STRING,
            department STRING,
            decision_maker BOOLEAN,
            raw_data VARIANT,
            sync_status STRING DEFAULT 'pending',
            error_message STRING,
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
            processed_date TIMESTAMP_NTZ
        )""",

        """CREATE OR REPLACE TABLE staging_opportunities (
            staging_id STRING PRIMARY KEY,
            external_id STRING,
            system_id STRING,
            company_external_id STRING,
            opportunity_name STRING,
            stage STRING,
            amount NUMBER(15,2),
            probability NUMBER(5,2),
            expected_close_date DATE,
            sales_rep_external_id STRING,
            competitor STRING,
            raw_data VARIANT,
            sync_status STRING DEFAULT 'pending',
            error_message STRING,
            created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
            processed_date TIMESTAMP_NTZ
        )""",

        # Insert sample data
        """INSERT INTO external_systems (system_id, system_name, system_type, api_endpoint, authentication_type, connection_status, sync_frequency) VALUES
        ('sys_001', 'Salesforce Production', 'CRM', 'https://api.salesforce.com/v1', 'oauth', 'active', 'hourly'),
        ('sys_002', 'HubSpot Marketing', 'CRM', 'https://api.hubapi.com/v3', 'api_key', 'active', 'daily'),
        ('sys_003', 'Microsoft Dynamics', 'CRM', 'https://api.dynamics.com/v9.0', 'oauth', 'active', 'hourly'),
        ('sys_004', 'Pipedrive Sales', 'CRM', 'https://api.pipedrive.com/v1', 'api_key', 'inactive', 'daily')""",

        """INSERT INTO sync_logs (log_id, system_id, sync_type, sync_status, records_processed, records_success, records_failed, sync_start_time, sync_end_time) VALUES
        ('log_001', 'sys_001', 'incremental', 'completed', 150, 148, 2, '2025-01-15 08:00:00', '2025-01-15 08:15:00'),
        ('log_002', 'sys_002', 'full', 'completed', 2500, 2485, 15, '2025-01-15 02:00:00', '2025-01-15 02:45:00'),
        ('log_003', 'sys_003', 'incremental', 'completed', 89, 89, 0, '2025-01-15 09:00:00', '2025-01-15 09:08:00')""",

        """INSERT INTO staging_companies (staging_id, external_id, system_id, company_name, industry, company_size, annual_revenue, website, sync_status) VALUES
        ('stg_comp_001', 'sf_001234', 'sys_001', 'Enterprise Tech Solutions', 'Technology', 'Large', 50000000.00, 'https://enterprisetech.com', 'processed'),
        ('stg_comp_002', 'hub_567890', 'sys_002', 'Global Manufacturing Corp', 'Manufacturing', 'Enterprise', *********.00, 'https://globalmfg.com', 'processed'),
        ('stg_comp_003', 'dyn_111222', 'sys_003', 'HealthCare Innovations', 'Healthcare', 'Medium', 25000000.00, 'https://healthinnovate.com', 'pending')""",

        "COMMIT"
    ]

    return execute_sql_commands(connection, commands, "Integration Schema")

def verify_data_population(connection):
    """Verify that data has been populated in all schemas"""
    cursor = connection.cursor()

    verification_queries = [
        # CRM_DATA schema
        ("CRM_DATA Companies", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.companies"),
        ("CRM_DATA Opportunities", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.opportunities"),
        ("CRM_DATA Leads", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.leads"),
        ("CRM_DATA Activities", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.activities"),
        ("CRM_DATA Sales Users", "SELECT COUNT(*) FROM salesgenie_ai.crm_data.sales_users"),

        # ANALYTICS schema
        ("ANALYTICS Forecasts", "SELECT COUNT(*) FROM salesgenie_ai.analytics.sales_forecasts"),
        ("ANALYTICS Metrics", "SELECT COUNT(*) FROM salesgenie_ai.analytics.historical_metrics"),
        ("ANALYTICS Churn Predictions", "SELECT COUNT(*) FROM salesgenie_ai.analytics.churn_predictions"),
        ("ANALYTICS Benchmarks", "SELECT COUNT(*) FROM salesgenie_ai.analytics.performance_benchmarks"),

        # AI_SERVICES schema
        ("AI_SERVICES Model Configs", "SELECT COUNT(*) FROM salesgenie_ai.ai_services.ai_model_configs"),
        ("AI_SERVICES Usage Logs", "SELECT COUNT(*) FROM salesgenie_ai.ai_services.cortex_usage_logs"),
        ("AI_SERVICES Training Data", "SELECT COUNT(*) FROM salesgenie_ai.ai_services.training_datasets"),
        ("AI_SERVICES Recommendations", "SELECT COUNT(*) FROM salesgenie_ai.ai_services.ai_recommendations"),

        # INTEGRATION schema
        ("INTEGRATION External Systems", "SELECT COUNT(*) FROM salesgenie_ai.integration.external_systems"),
        ("INTEGRATION Sync Logs", "SELECT COUNT(*) FROM salesgenie_ai.integration.sync_logs"),
        ("INTEGRATION Staging Companies", "SELECT COUNT(*) FROM salesgenie_ai.integration.staging_companies"),
    ]

    print("\n🔍 Data Population Verification:")
    print("=" * 60)

    all_verified = True

    for description, query in verification_queries:
        try:
            cursor.execute(query)
            count = cursor.fetchone()[0]
            status = "✅" if count > 0 else "❌"
            print(f"{status} {description}: {count} records")
            if count == 0:
                all_verified = False
        except Exception as e:
            print(f"❌ {description}: Error - {str(e)}")
            all_verified = False

    cursor.close()
    return all_verified

def main():
    """Main function"""
    print("=" * 60)
    print("🚀 SALESGENIE AI - COMPLETE SCHEMA POPULATION (PYTHON)")
    print("=" * 60)
    print("This script will populate ALL schemas with comprehensive data:")
    print("• CRM_DATA - Core sales data (already exists)")
    print("• ANALYTICS - Advanced analytics and ML models")
    print("• AI_SERVICES - AI configurations and usage tracking")
    print("• INTEGRATION - External system integrations")
    print("=" * 60)

    # Connect to Snowflake
    connection = get_snowflake_connection()
    if not connection:
        logger.error("Cannot proceed without Snowflake connection")
        return False

    try:
        success_count = 0
        total_schemas = 3  # Analytics, AI Services, and Integration (CRM_DATA already exists)

        # Create Analytics Schema
        if create_analytics_schema(connection):
            success_count += 1

        # Create AI Services Schema
        if create_ai_services_schema(connection):
            success_count += 1

        # Create Integration Schema
        if create_integration_schema(connection):
            success_count += 1

        if success_count == total_schemas:
            logger.info("✅ All schemas populated successfully!")

            # Verify data population
            logger.info("Verifying data population...")
            if verify_data_population(connection):
                print("\n" + "=" * 60)
                print("✅ SCHEMA POPULATION COMPLETED SUCCESSFULLY!")
                print("=" * 60)
                print("\n🎉 Your SalesGenie AI solution now has comprehensive data across all schemas!")
                print("\nWhat's been populated:")
                print("• 📊 CRM Data: Companies, contacts, leads, opportunities, activities")
                print("• 📈 Analytics: Forecasts, metrics, churn predictions, benchmarks")
                print("• 🤖 AI Services: Model configs, usage logs, training data, recommendations")
                print("• 🔗 Integration: External systems, sync logs, staging data")
                print("\nNext steps:")
                print("1. Test the Streamlit app: 'streamlit run streamlit_app.py'")
                print("2. Try advanced analytics queries on all schemas")
                return True
            else:
                logger.warning("Some data verification checks failed")
                return False
        else:
            logger.warning(f"Only {success_count}/{total_schemas} schemas populated successfully")
            return False

    except Exception as e:
        logger.error(f"Schema population failed: {str(e)}")
        return False

    finally:
        if connection:
            connection.close()

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n" + "=" * 60)
        print("❌ SCHEMA POPULATION FAILED")
        print("=" * 60)
        print("Please check the errors above and try again")
        sys.exit(1)
