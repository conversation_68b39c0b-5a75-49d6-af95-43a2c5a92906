# 📁 SalesGenie AI - Project Structure

## Overview
This document outlines the complete project structure for SalesGenie AI, a conversational AI platform for sales excellence built on Snowflake Cortex.

```
salesgenie-ai/
├── README.md                          # Main project documentation
├── DEPLOYMENT_GUIDE.md               # Comprehensive deployment instructions
├── PROJECT_STRUCTURE.md              # This file - project organization
├── requirements.txt                   # Python dependencies
├── .gitignore                        # Git ignore patterns
│
├── config/                           # Configuration files
│   └── snowflake_config.template.json # Snowflake connection template
│
├── sql/                              # Database setup and configuration
│   ├── 01_setup_database.sql        # Database, schema, and table creation
│   ├── 02_sample_data.sql           # Sample CRM data for demonstration
│   ├── 03_ai_services_setup.sql     # Cortex AI services configuration
│   └── 04_advanced_analytics.sql    # ML models and advanced analytics
│
├── semantic_models/                  # Cortex Analyst semantic models
│   └── sales_crm_semantic_model.yaml # Main semantic model for CRM data
│
├── scripts/                          # Deployment and utility scripts
│   └── deploy_to_snowflake.py       # Automated deployment script
│
├── demo/                             # Demo materials and scenarios
│   └── demo_scenarios.md            # Hackathon demo script and scenarios
│
└── streamlit_app.py                  # Main Streamlit application
```

## 📂 Directory Details

### `/config/`
**Purpose**: Configuration files and templates
- `snowflake_config.template.json`: Template for Snowflake connection parameters
- Contains settings for Cortex models, app configuration, and environment variables

### `/sql/`
**Purpose**: Database setup and SQL scripts
- **01_setup_database.sql**: Creates database structure, roles, warehouses, and core tables
- **02_sample_data.sql**: Populates database with realistic sample CRM data
- **03_ai_services_setup.sql**: Configures Cortex Search, AI functions, and enhanced views
- **04_advanced_analytics.sql**: Creates ML models, predictive functions, and analytics views

### `/semantic_models/`
**Purpose**: Cortex Analyst semantic models
- **sales_crm_semantic_model.yaml**: Defines business logic, relationships, and verified queries for conversational AI

### `/scripts/`
**Purpose**: Automation and deployment scripts
- **deploy_to_snowflake.py**: Complete deployment automation with error handling and verification

### `/demo/`
**Purpose**: Demo materials for hackathon presentation
- **demo_scenarios.md**: Detailed demo script with scenarios and talking points

## 🗄️ Database Schema

### Core CRM Tables
```
companies          # Customer companies and prospects
├── company_id (PK)
├── company_name
├── industry
├── company_size
├── annual_revenue
└── location_data

contacts           # Individual contacts at companies
├── contact_id (PK)
├── company_id (FK)
├── personal_info
├── job_details
└── contact_preferences

leads              # Sales leads and prospects
├── lead_id (PK)
├── company_id (FK)
├── contact_id (FK)
├── lead_scoring
├── qualification_data
└── follow_up_info

opportunities      # Sales opportunities and deals
├── opportunity_id (PK)
├── company_id (FK)
├── deal_details
├── stage_info
├── probability
└── financial_data

activities         # Sales activities and interactions
├── activity_id (PK)
├── related_records (FKs)
├── activity_type
├── content
└── completion_status

products           # Product catalog
├── product_id (PK)
├── product_details
├── pricing
└── categories

sales_users        # Sales team members
├── user_id (PK)
├── user_details
├── territory
└── performance_data
```

### Analytics Views
```
ai_enhanced_pipeline    # Pipeline with AI risk assessment
ai_enhanced_leads      # Leads with AI scoring
ai_sales_performance   # Sales rep performance with AI insights
sales_forecast         # Predictive sales forecasting
customer_health_score  # Customer health and churn risk
territory_analysis     # Territory performance analysis
sales_velocity_analysis # Deal velocity and conversion metrics
competitive_analysis   # Competitive win/loss analysis
```

## 🤖 AI Services Architecture

### Cortex LLM Functions
- **COMPLETE**: Multi-model approach for different tasks
- **SENTIMENT**: Activity and note sentiment analysis
- **SUMMARIZE**: Meeting and interaction summaries
- **CLASSIFY_TEXT**: Lead and opportunity classification
- **EMBED_TEXT**: Semantic similarity and search

### Cortex Search Services
- **company_search_service**: Intelligent company lookup
- **lead_search_service**: Lead discovery and matching
- **opportunity_search_service**: Deal similarity search
- **activity_search_service**: Historical interaction search

### Custom AI Functions
- **ai_lead_scoring()**: Enhanced lead scoring algorithm
- **generate_email_template()**: AI-powered email generation
- **analyze_opportunity_risk()**: Deal risk assessment
- **predict_deal_closure_probability()**: ML-based closure prediction
- **recommend_next_action()**: Intelligent action recommendations

## 🎯 Key Features Implementation

### 1. Conversational Interface
- **File**: `streamlit_app.py`
- **Technology**: Streamlit + Cortex Analyst
- **Features**: Natural language queries, multi-turn conversations, voice input

### 2. AI-Powered Analytics
- **Files**: `sql/03_ai_services_setup.sql`, `sql/04_advanced_analytics.sql`
- **Technology**: Cortex LLMs + Custom ML functions
- **Features**: Predictive scoring, risk assessment, performance insights

### 3. Intelligent Search
- **File**: `sql/03_ai_services_setup.sql`
- **Technology**: Cortex Search
- **Features**: Semantic search, similarity matching, content discovery

### 4. Workflow Automation
- **Files**: `streamlit_app.py`, `sql/03_ai_services_setup.sql`
- **Technology**: Cortex LLMs + Custom functions
- **Features**: Task prioritization, email generation, follow-up scheduling

## 🚀 Deployment Flow

### Automated Deployment
1. **Environment Setup**: Verify prerequisites and dependencies
2. **Database Creation**: Execute `01_setup_database.sql`
3. **Data Loading**: Execute `02_sample_data.sql`
4. **AI Services**: Execute `03_ai_services_setup.sql`
5. **Analytics Setup**: Execute `04_advanced_analytics.sql`
6. **Semantic Model**: Upload and configure Cortex Analyst model
7. **Streamlit App**: Deploy conversational interface
8. **Verification**: Test all components and data integrity

### Manual Deployment
- Step-by-step execution of SQL scripts
- Manual configuration of Cortex services
- Individual component testing and validation

## 📊 Data Flow

### Input Sources
- **Sample Data**: Realistic CRM data for demonstration
- **Real-time Integration**: APIs for live CRM system connection
- **User Interactions**: Conversational queries and commands

### Processing Pipeline
1. **Data Ingestion**: Raw data → Snowflake tables
2. **AI Enhancement**: Cortex services → Enriched data
3. **Analytics Processing**: ML models → Insights and predictions
4. **User Interface**: Streamlit → Conversational interactions

### Output Destinations
- **Streamlit Dashboard**: Interactive visualizations and chat
- **API Responses**: JSON data for external integrations
- **Automated Actions**: CRM updates and workflow triggers

## 🔧 Configuration Management

### Environment Variables
- Snowflake connection parameters
- Cortex model preferences
- Application settings and features

### Model Configuration
- LLM model selection and parameters
- Search service configuration
- ML model hyperparameters

### Security Settings
- Role-based access control
- Data masking and privacy
- API authentication and authorization

## 📈 Monitoring and Observability

### Performance Metrics
- Query response times
- AI service usage and costs
- User interaction patterns

### Data Quality
- Data freshness and completeness
- AI prediction accuracy
- Search relevance scores

### Business Metrics
- User adoption and engagement
- Sales process improvements
- ROI and business impact

---

## 🎯 Next Steps for Enhancement

### Phase 1: Core Functionality
- ✅ Database setup and sample data
- ✅ AI services configuration
- ✅ Conversational interface
- ✅ Basic analytics and insights

### Phase 2: Advanced Features
- 🔄 Real-time CRM integration
- 🔄 Advanced ML models
- 🔄 Mobile application
- 🔄 Voice interface enhancement

### Phase 3: Enterprise Features
- 📋 Multi-tenant architecture
- 📋 Advanced security features
- 📋 Custom dashboard builder
- 📋 API marketplace integration

### Phase 4: AI Innovation
- 📋 Autonomous sales agents
- 📋 Predictive customer modeling
- 📋 Advanced NLP capabilities
- 📋 Computer vision integration

---

**This project structure provides a solid foundation for building and scaling SalesGenie AI as a comprehensive sales intelligence platform powered by Snowflake Cortex.**
