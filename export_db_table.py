#!/usr/bin/env python3
"""
Export all tables from Snowflake SALESGENIE_AI database to CSV files
"""

import snowflake.connector
import pandas as pd
import os
import json
import getpass
import sys
from datetime import datetime

def get_snowflake_connection():
    """Get Snowflake connection with credentials"""

    # Load config
    config_path = "config/snowflake_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {
            "account": "GNBXJLF-PKB97538",
            "user": "ASHUTOSH1",
            "warehouse": "SALESGENIE_AI_WH",
            "database": "SALESGENIE_AI",
            "schema": "CRM_DATA",
            "role": "ACCOUNTADMIN"
        }

    # Get password securely
    if config.get("password") == "XXXXXXXXXXXXXXXXXX" or not config.get("password"):
        password = getpass.getpass(f"Enter password for {config['user']}: ")
    else:
        password = config["password"]

    try:
        # Create connection
        conn = snowflake.connector.connect(
            account=config["account"],
            user=config["user"],
            password=password,
            warehouse=config["warehouse"],
            database=config["database"],
            role=config["role"]
        )
        print(f"✅ Connected to Snowflake as {config['user']}")
        return conn
    except Exception as e:
        print(f"❌ Failed to connect to Snowflake: {e}")
        sys.exit(1)

def get_schemas(conn):
    """Get all schemas from the database"""
    cur = conn.cursor()
    try:
        cur.execute("SHOW SCHEMAS IN DATABASE SALESGENIE_AI;")
        schemas = [row[1] for row in cur.fetchall() if row[1] != 'INFORMATION_SCHEMA']
        print(f"📋 Found {len(schemas)} schemas: {', '.join(schemas)}")
        return schemas
    except Exception as e:
        print(f"❌ Failed to get schemas: {e}")
        return []
    finally:
        cur.close()

def get_tables(conn, schema):
    """Get all tables from a schema"""
    cur = conn.cursor()
    try:
        cur.execute(f"SHOW TABLES IN SCHEMA SALESGENIE_AI.{schema};")
        tables = [row[1] for row in cur.fetchall()]
        print(f"  📊 Schema {schema}: {len(tables)} tables")
        return tables
    except Exception as e:
        print(f"❌ Failed to get tables for schema {schema}: {e}")
        return []
    finally:
        cur.close()

def export_table(conn, schema, table, output_dir):
    """Export a single table to CSV"""
    query = f"SELECT * FROM SALESGENIE_AI.{schema}.{table};"
    try:
        print(f"  📤 Exporting {schema}.{table}...", end=" ")
        df = pd.read_sql(query, conn)
        filename = f"{schema}__{table}.csv"
        filepath = os.path.join(output_dir, filename)
        df.to_csv(filepath, index=False)
        print(f"✅ ({len(df)} rows)")
        return True
    except Exception as e:
        print(f"❌ Failed: {e}")
        return False

def main():
    """Main export function"""
    print("🚀 Starting Snowflake table export...")
    print(f"📅 Export started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Create output folder
    output_dir = "salesgenie_export"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 Output directory: {output_dir}")

    # Get connection
    conn = get_snowflake_connection()

    try:
        # Get all schemas
        schemas = get_schemas(conn)
        if not schemas:
            print("❌ No schemas found. Exiting.")
            return

        # Export all tables
        total_exported = 0
        total_failed = 0

        for schema in schemas:
            print(f"\n🔍 Processing schema: {schema}")
            tables = get_tables(conn, schema)

            for table in tables:
                if export_table(conn, schema, table, output_dir):
                    total_exported += 1
                else:
                    total_failed += 1

        # Summary
        print(f"\n📊 Export Summary:")
        print(f"   ✅ Successfully exported: {total_exported} tables")
        print(f"   ❌ Failed exports: {total_failed} tables")
        print(f"   📁 Files saved in: {output_dir}")
        print(f"   📅 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    except Exception as e:
        print(f"❌ Export failed: {e}")
    finally:
        conn.close()
        print("🔌 Connection closed.")

if __name__ == "__main__":
    main()
