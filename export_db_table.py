import snowflake.connector
import pandas as pd
import os

# Snowflake connection configuration
conn = snowflake.connector.connect(   
    account: "GNBXJLF-PKB97538",
    user: "ASHUTOSH1",
    password: "**************",
    warehouse: "SALESGENIE_AI_WH",
    database: "SALESGENIE_AI",
    role: "ACCOUNTADMIN"
)

# Create output folder
output_dir = "salesgenie_export"
os.makedirs(output_dir, exist_ok=True)

def get_schemas():
    cur = conn.cursor()
    cur.execute("SHOW SCHEMAS IN DATABASE SALESGENIE_AI;")
    return [row[1] for row in cur.fetchall() if row[1] != 'INFORMATION_SCHEMA']

def get_tables(schema):
    cur = conn.cursor()
    cur.execute(f"SHOW TABLES IN SCHEMA SALESGENIE_AI.{schema};")
    return [row[1] for row in cur.fetchall()]

def export_table(schema, table):
    query = f"SELECT * FROM SALESGENIE_AI.{schema}.{table};"
    try:
        df = pd.read_sql(query, conn)
        filename = f"{schema}__{table}.csv"
        df.to_csv(os.path.join(output_dir, filename), index=False)
        print(f"Exported: {schema}.{table}")
    except Exception as e:
        print(f"Failed to export {schema}.{table}: {e}")

# Process all tables
for schema in get_schemas():
    for table in get_tables(schema):
        export_table(schema, table)

conn.close()
print(f"\n✅ Export complete. CSV files saved in: {output_dir}")
