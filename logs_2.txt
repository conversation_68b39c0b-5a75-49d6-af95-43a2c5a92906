2025-05-29 03:30:50,838 - DEBUG - Request guid: 6f9a07ff-3b32-4ad0-bf7c-0b161198828a
2025-05-29 03:30:50,840 - DEBUG - socket timeout: 60
2025-05-29 03:30:51,331 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=73980584-281d-4dc6-b196-29e87e7e4fe6&request_guid=6f9a07ff-3b32-4ad0-bf7c-0b161198828a HTTP/1.1" 200 None
2025-05-29 03:30:51,331 - DEBUG - SUCCESS
2025-05-29 03:30:51,331 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:51,331 - DEBUG - ret[code] = None, after post request
2025-05-29 03:30:51,331 - DEBUG - Query id: 01bca8a9-0205-6cf9-0000-000a0e5fe00d
2025-05-29 03:30:51,331 - DEBUG - deserialize_json_dict() called: data from server: {'entries': [{'id': 0, 'timestamp': 1748469661341247, 'priority': 0, 'context': 'CObn1oBw'}]}
2025-05-29 03:30:51,331 - DEBUG - Cache Entry: (0, 1748469659338693, 0)
2025-05-29 03:30:51,331 - DEBUG - deserialize {'id': 0, 'timestamp': 1748469661341247, 'priority': 0, 'context': 'CObn1oBw'}
2025-05-29 03:30:51,331 - DEBUG - sync_priority_map called priority_map size = 0, new_priority_map size = 1
2025-05-29 03:30:51,331 - DEBUG - trim_cache() called. treeSet size is 1 and cache capacity is 5
2025-05-29 03:30:51,331 - DEBUG - trim_cache() returns. treeSet size is 1 and cache capacity is 5
2025-05-29 03:30:51,336 - DEBUG - deserialize_json_dict() returns  
2025-05-29 03:30:51,336 - DEBUG - Cache Entry: (0, 1748469661341247, 0)
2025-05-29 03:30:51,337 - DEBUG - sfqid: 01bca8a9-0205-6cf9-0000-000a0e5fe00d
2025-05-29 03:30:51,337 - DEBUG - query execution done
2025-05-29 03:30:51,337 - DEBUG - SUCCESS
2025-05-29 03:30:51,337 - DEBUG - PUT OR GET: False
2025-05-29 03:30:51,337 - DEBUG - Query result format: json        
2025-05-29 03:30:51,337 - DEBUG - parsing for result batch id: 1   
2025-05-29 03:30:51,337 - DEBUG - Number of results in first chunk: 1
2025-05-29 03:30:51,337 - DEBUG - Statement executed successfully  
2025-05-29 03:30:51,337 - DEBUG - Executing statement 17/17        
2025-05-29 03:30:51,337 - DEBUG - executing SQL/command
2025-05-29 03:30:51,337 - DEBUG - query: [COMMIT]
2025-05-29 03:30:51,339 - DEBUG - binding: [COMMIT] with input=[None], processed=[{}]
2025-05-29 03:30:51,339 - DEBUG - sequence counter: 23
2025-05-29 03:30:51,340 - DEBUG - Request id: 346d757a-6f8f-4166-a565-70dcc19f73c2
2025-05-29 03:30:51,340 - DEBUG - running query [COMMIT]
2025-05-29 03:30:51,340 - DEBUG - is_file_transfer: True
2025-05-29 03:30:51,340 - DEBUG - _cmd_query
2025-05-29 03:30:51,340 - DEBUG - serialize_to_dict() called       
2025-05-29 03:30:51,340 - DEBUG - Cache Entry: (0, 1748469661341247, 0)
2025-05-29 03:30:51,340 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748469661341247, 'priority': 0, 'context': {'base64Data': 'CObn1oBw'}}]}
2025-05-29 03:30:51,341 - DEBUG - sql=[COMMIT], sequence_id=[23], is_file_transfer=[False]
2025-05-29 03:30:51,342 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 03:30:51,342 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:51,342 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 03:30:51,343 - DEBUG - Request guid: b01d0a07-0106-4392-8b1b-cf7daf5a7837
2025-05-29 03:30:51,343 - DEBUG - socket timeout: 60
2025-05-29 03:30:51,732 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=346d757a-6f8f-4166-a565-70dcc19f73c2&request_guid=b01d0a07-0106-4392-8b1b-cf7daf5a7837 HTTP/1.1" 200 None
2025-05-29 03:30:51,732 - DEBUG - SUCCESS
2025-05-29 03:30:51,732 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:51,732 - DEBUG - ret[code] = None, after post request
2025-05-29 03:30:51,732 - DEBUG - Query id: 01bca8a9-0205-6eec-0000-000a0e5ff00d
2025-05-29 03:30:51,744 - DEBUG - deserialize_json_dict() called: data from server: {'entries': [{'id': 0, 'timestamp': 1748469661769681, 'priority': 0, 'context': 'CLL31oBw'}]}
2025-05-29 03:30:51,746 - DEBUG - Cache Entry: (0, 1748469661341247, 0)
2025-05-29 03:30:51,746 - DEBUG - deserialize {'id': 0, 'timestamp': 1748469661769681, 'priority': 0, 'context': 'CLL31oBw'}
2025-05-29 03:30:51,746 - DEBUG - sync_priority_map called priority_map size = 0, new_priority_map size = 1
2025-05-29 03:30:51,746 - DEBUG - trim_cache() called. treeSet size is 1 and cache capacity is 5
2025-05-29 03:30:51,746 - DEBUG - trim_cache() returns. treeSet size is 1 and cache capacity is 5
2025-05-29 03:30:51,746 - DEBUG - deserialize_json_dict() returns  
2025-05-29 03:30:51,746 - DEBUG - Cache Entry: (0, 1748469661769681, 0)
2025-05-29 03:30:51,748 - DEBUG - sfqid: 01bca8a9-0205-6eec-0000-000a0e5ff00d
2025-05-29 03:30:51,748 - DEBUG - query execution done
2025-05-29 03:30:51,748 - DEBUG - SUCCESS
2025-05-29 03:30:51,748 - DEBUG - PUT OR GET: False
2025-05-29 03:30:51,750 - DEBUG - Query result format: json        
2025-05-29 03:30:51,750 - DEBUG - parsing for result batch id: 1   
2025-05-29 03:30:51,750 - DEBUG - Number of results in first chunk: 1
2025-05-29 03:30:51,751 - DEBUG - Statement executed successfully  
2025-05-29 03:30:51,751 - INFO - Successfully executed SQL file: sql/02_sample_data.sql
2025-05-29 03:30:51,751 - INFO - Executing SQL file: sql/03_ai_services_setup.sql
2025-05-29 03:30:51,752 - DEBUG - cursor
2025-05-29 03:30:51,753 - DEBUG - Executing statement 2/19
2025-05-29 03:30:51,753 - DEBUG - executing SQL/command
2025-05-29 03:30:51,753 - DEBUG - query: [USE WAREHOUSE salesgenie_ai_wh]
2025-05-29 03:30:51,753 - DEBUG - binding: [USE WAREHOUSE salesgenie_ai_wh] with input=[None], processed=[{}]
2025-05-29 03:30:51,753 - DEBUG - sequence counter: 24
2025-05-29 03:30:51,753 - DEBUG - Request id: 580523e1-7c9b-435f-9f36-88aadc1a3006
2025-05-29 03:30:51,753 - DEBUG - running query [USE WAREHOUSE salesgenie_ai_wh]
2025-05-29 03:30:51,753 - DEBUG - is_file_transfer: True
2025-05-29 03:30:51,753 - DEBUG - _cmd_query
2025-05-29 03:30:51,753 - DEBUG - serialize_to_dict() called       
2025-05-29 03:30:51,753 - DEBUG - Cache Entry: (0, 1748469661769681, 0)
2025-05-29 03:30:51,753 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748469661769681, 'priority': 0, 'context': {'base64Data': 'CLL31oBw'}}]}
2025-05-29 03:30:51,753 - DEBUG - sql=[USE WAREHOUSE salesgenie_ai_wh], sequence_id=[24], is_file_transfer=[False]
2025-05-29 03:30:51,753 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 03:30:51,753 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:51,753 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 03:30:51,753 - DEBUG - Request guid: 686dfbe6-8f85-44a2-a6ae-b62519b63439
2025-05-29 03:30:51,757 - DEBUG - socket timeout: 60
2025-05-29 03:30:52,150 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=580523e1-7c9b-435f-9f36-88aadc1a3006&request_guid=686dfbe6-8f85-44a2-a6ae-b62519b63439 HTTP/1.1" 200 None
2025-05-29 03:30:52,150 - DEBUG - SUCCESS
2025-05-29 03:30:52,150 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:52,150 - DEBUG - ret[code] = 002043, after post request
2025-05-29 03:30:52,150 - DEBUG - Query id: 01bca8a9-0205-696d-0000-000a0e5fa155
2025-05-29 03:30:52,150 - DEBUG - sfqid: 01bca8a9-0205-696d-0000-000a0e5fa155
2025-05-29 03:30:52,150 - DEBUG - query execution done
2025-05-29 03:30:52,150 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002043', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8a9-0205-696d-0000-000a0e5fa155', 'line': -1, 'pos': -1, 'type': 'COMPILATION'}, 'code': '002043', 'message': 'SQL compilation error:\nObject does not exist, or operation cannot be performed.', 'success': False, 'headers': None}
2025-05-29 03:30:52,150 - WARNING - Statement 2 failed: 002043 (02000): 01bca8a9-0205-696d-0000-000a0e5fa155: SQL compilation error:  
Object does not exist, or operation cannot be performed.
2025-05-29 03:30:52,150 - DEBUG - Executing statement 3/19
2025-05-29 03:30:52,150 - DEBUG - executing SQL/command
2025-05-29 03:30:52,150 - DEBUG - query: [USE DATABASE salesgenie_ai]
2025-05-29 03:30:52,150 - DEBUG - binding: [USE DATABASE salesgenie_ai] with input=[None], processed=[{}]
2025-05-29 03:30:52,150 - DEBUG - sequence counter: 25
2025-05-29 03:30:52,150 - DEBUG - Request id: 807e7573-83a5-49ce-96e6-3a158133b661
2025-05-29 03:30:52,158 - DEBUG - running query [USE DATABASE salesgenie_ai]
2025-05-29 03:30:52,158 - DEBUG - is_file_transfer: True
2025-05-29 03:30:52,158 - DEBUG - _cmd_query
2025-05-29 03:30:52,158 - DEBUG - serialize_to_dict() called       
2025-05-29 03:30:52,158 - DEBUG - Cache Entry: (0, 1748469661769681, 0)
2025-05-29 03:30:52,158 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748469661769681, 'priority': 0, 'context': {'base64Data': 'CLL31oBw'}}]}
2025-05-29 03:30:52,159 - DEBUG - sql=[USE DATABASE salesgenie_ai], sequence_id=[25], is_file_transfer=[False]
2025-05-29 03:30:52,160 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 03:30:52,160 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:52,160 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 03:30:52,162 - DEBUG - Request guid: ebaaa77f-f244-4a2f-8be7-ec9a0ed9943c
2025-05-29 03:30:52,162 - DEBUG - socket timeout: 60
2025-05-29 03:30:52,562 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=807e7573-83a5-49ce-96e6-3a158133b661&request_guid=ebaaa77f-f244-4a2f-8be7-ec9a0ed9943c HTTP/1.1" 200 None
2025-05-29 03:30:52,563 - DEBUG - SUCCESS
2025-05-29 03:30:52,563 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:52,563 - DEBUG - ret[code] = 002043, after post request
2025-05-29 03:30:52,563 - DEBUG - Query id: 01bca8a9-0205-6cf8-0000-000a0e5fd015
2025-05-29 03:30:52,563 - DEBUG - sfqid: 01bca8a9-0205-6cf8-0000-000a0e5fd015
2025-05-29 03:30:52,563 - DEBUG - query execution done
2025-05-29 03:30:52,563 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002043', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8a9-0205-6cf8-0000-000a0e5fd015', 'line': -1, 'pos': -1, 'type': 'COMPILATION'}, 'code': '002043', 'message': 'SQL compilation error:\nObject does not exist, or operation cannot be performed.', 'success': False, 'headers': None}
2025-05-29 03:30:52,563 - WARNING - Statement 3 failed: 002043 (02000): 01bca8a9-0205-6cf8-0000-000a0e5fd015: SQL compilation error:  
Object does not exist, or operation cannot be performed.
2025-05-29 03:30:52,563 - DEBUG - Executing statement 16/19        
2025-05-29 03:30:52,571 - DEBUG - executing SQL/command
2025-05-29 03:30:52,571 - DEBUG - query: [COMMIT]
2025-05-29 03:30:52,571 - DEBUG - binding: [COMMIT] with input=[None], processed=[{}]
2025-05-29 03:30:52,571 - DEBUG - sequence counter: 26
2025-05-29 03:30:52,571 - DEBUG - Request id: 4571a40a-3303-4747-b532-1b45fc5be9a0
2025-05-29 03:30:52,571 - DEBUG - running query [COMMIT]
2025-05-29 03:30:52,571 - DEBUG - is_file_transfer: True
2025-05-29 03:30:52,571 - DEBUG - _cmd_query
2025-05-29 03:30:52,571 - DEBUG - serialize_to_dict() called       
2025-05-29 03:30:52,571 - DEBUG - Cache Entry: (0, 1748469661769681, 0)
2025-05-29 03:30:52,571 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748469661769681, 'priority': 0, 'context': {'base64Data': 'CLL31oBw'}}]}
2025-05-29 03:30:52,575 - DEBUG - sql=[COMMIT], sequence_id=[26], is_file_transfer=[False]
2025-05-29 03:30:52,575 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 03:30:52,578 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:52,578 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 03:30:52,578 - DEBUG - Request guid: 5b193c46-9c5c-4d7d-9a6a-5f5aa3feda75
2025-05-29 03:30:52,578 - DEBUG - socket timeout: 60
2025-05-29 03:30:52,979 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=4571a40a-3303-4747-b532-1b45fc5be9a0&request_guid=5b193c46-9c5c-4d7d-9a6a-5f5aa3feda75 HTTP/1.1" 200 None
2025-05-29 03:30:52,979 - DEBUG - SUCCESS
2025-05-29 03:30:52,979 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:52,979 - DEBUG - ret[code] = None, after post request
2025-05-29 03:30:52,979 - DEBUG - Query id: 01bca8a9-0205-68ee-0000-000a0e5f82fd
2025-05-29 03:30:52,979 - DEBUG - deserialize_json_dict() called: data from server: {'entries': [{'id': 0, 'timestamp': 1748469662994415, 'priority': 0, 'context': 'CLrH1oBw'}]}
2025-05-29 03:30:52,984 - DEBUG - Cache Entry: (0, 1748469661769681, 0)
2025-05-29 03:30:52,985 - DEBUG - deserialize {'id': 0, 'timestamp': 1748469662994415, 'priority': 0, 'context': 'CLrH1oBw'}
2025-05-29 03:30:52,986 - DEBUG - sync_priority_map called priority_map size = 0, new_priority_map size = 1
2025-05-29 03:30:52,986 - DEBUG - trim_cache() called. treeSet size is 1 and cache capacity is 5
2025-05-29 03:30:52,986 - DEBUG - trim_cache() returns. treeSet size is 1 and cache capacity is 5
2025-05-29 03:30:52,986 - DEBUG - deserialize_json_dict() returns  
2025-05-29 03:30:52,986 - DEBUG - Cache Entry: (0, 1748469662994415, 0)
2025-05-29 03:30:52,986 - DEBUG - sfqid: 01bca8a9-0205-68ee-0000-000a0e5f82fd
2025-05-29 03:30:52,986 - DEBUG - query execution done
2025-05-29 03:30:52,988 - DEBUG - SUCCESS
2025-05-29 03:30:52,988 - DEBUG - PUT OR GET: False
2025-05-29 03:30:52,988 - DEBUG - Query result format: json        
2025-05-29 03:30:52,988 - DEBUG - parsing for result batch id: 1   
2025-05-29 03:30:52,988 - DEBUG - Number of results in first chunk: 1
2025-05-29 03:30:52,989 - DEBUG - Statement executed successfully  
2025-05-29 03:30:52,989 - INFO - Successfully executed SQL file: sql/03_ai_services_setup.sql
2025-05-29 03:30:52,990 - INFO - Executing SQL file: sql/04_advanced_analytics.sql
2025-05-29 03:30:52,991 - DEBUG - cursor
2025-05-29 03:30:52,991 - DEBUG - Executing statement 2/12
2025-05-29 03:30:52,992 - DEBUG - executing SQL/command
2025-05-29 03:30:52,992 - DEBUG - query: [USE WAREHOUSE salesgenie_ai_wh]
2025-05-29 03:30:52,992 - DEBUG - binding: [USE WAREHOUSE salesgenie_ai_wh] with input=[None], processed=[{}]
2025-05-29 03:30:52,992 - DEBUG - sequence counter: 27
2025-05-29 03:30:52,992 - DEBUG - Request id: f772b1ca-7885-45d9-95df-7bc7028fb819
2025-05-29 03:30:52,993 - DEBUG - running query [USE WAREHOUSE salesgenie_ai_wh]
2025-05-29 03:30:52,993 - DEBUG - is_file_transfer: True
2025-05-29 03:30:52,993 - DEBUG - _cmd_query
2025-05-29 03:30:52,993 - DEBUG - serialize_to_dict() called       
2025-05-29 03:30:52,993 - DEBUG - Cache Entry: (0, 1748469662994415, 0)
2025-05-29 03:30:52,993 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748469662994415, 'priority': 0, 'context': {'base64Data': 'CLrH1oBw'}}]}
2025-05-29 03:30:52,993 - DEBUG - sql=[USE WAREHOUSE salesgenie_ai_wh], sequence_id=[27], is_file_transfer=[False]
2025-05-29 03:30:52,996 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 03:30:52,996 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:52,996 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 03:30:52,997 - DEBUG - Request guid: 61beceff-3e17-48a2-b4fa-bc2de6ddf43c
2025-05-29 03:30:52,997 - DEBUG - socket timeout: 60
2025-05-29 03:30:53,481 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=f772b1ca-7885-45d9-95df-7bc7028fb819&request_guid=61beceff-3e17-48a2-b4fa-bc2de6ddf43c HTTP/1.1" 200 None
2025-05-29 03:30:53,483 - DEBUG - SUCCESS
2025-05-29 03:30:53,483 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:53,483 - DEBUG - ret[code] = 002043, after post request
2025-05-29 03:30:53,483 - DEBUG - Query id: 01bca8a9-0205-67fa-0000-000a0e5f9455
2025-05-29 03:30:53,483 - DEBUG - sfqid: 01bca8a9-0205-67fa-0000-000a0e5f9455
2025-05-29 03:30:53,483 - DEBUG - query execution done
2025-05-29 03:30:53,483 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002043', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8a9-0205-67fa-0000-000a0e5f9455', 'line': -1, 'pos': -1, 'type': 'COMPILATION'}, 'code': '002043', 'message': 'SQL compilation error:\nObject does not exist, or operation cannot be performed.', 'success': False, 'headers': None}
2025-05-29 03:30:53,483 - WARNING - Statement 2 failed: 002043 (02000): 01bca8a9-0205-67fa-0000-000a0e5f9455: SQL compilation error:  
Object does not exist, or operation cannot be performed.
2025-05-29 03:30:53,483 - DEBUG - Executing statement 3/12
2025-05-29 03:30:53,483 - DEBUG - executing SQL/command
2025-05-29 03:30:53,483 - DEBUG - query: [USE DATABASE salesgenie_ai]
2025-05-29 03:30:53,483 - DEBUG - binding: [USE DATABASE salesgenie_ai] with input=[None], processed=[{}]
2025-05-29 03:30:53,483 - DEBUG - sequence counter: 28
2025-05-29 03:30:53,483 - DEBUG - Request id: 29824a2c-1ab3-4024-9b78-782eaeaf9b0d
2025-05-29 03:30:53,483 - DEBUG - running query [USE DATABASE salesgenie_ai]
2025-05-29 03:30:53,483 - DEBUG - is_file_transfer: True
2025-05-29 03:30:53,483 - DEBUG - _cmd_query
2025-05-29 03:30:53,483 - DEBUG - serialize_to_dict() called       
2025-05-29 03:30:53,483 - DEBUG - Cache Entry: (0, 1748469662994415, 0)
2025-05-29 03:30:53,483 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748469662994415, 'priority': 0, 'context': {'base64Data': 'CLrH1oBw'}}]}
2025-05-29 03:30:53,483 - DEBUG - sql=[USE DATABASE salesgenie_ai], sequence_id=[28], is_file_transfer=[False]
2025-05-29 03:30:53,489 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 03:30:53,489 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:53,489 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 03:30:53,489 - DEBUG - Request guid: c8490b07-bc86-481c-933a-a9ec8f8efbde
2025-05-29 03:30:53,489 - DEBUG - socket timeout: 60
2025-05-29 03:30:53,866 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=29824a2c-1ab3-4024-9b78-782eaeaf9b0d&request_guid=c8490b07-bc86-481c-933a-a9ec8f8efbde HTTP/1.1" 200 None
2025-05-29 03:30:53,866 - DEBUG - SUCCESS
2025-05-29 03:30:53,866 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:53,866 - DEBUG - ret[code] = 002043, after post request
2025-05-29 03:30:53,866 - DEBUG - Query id: 01bca8a9-0205-67e9-0000-000a0e5f4291
2025-05-29 03:30:53,866 - DEBUG - sfqid: 01bca8a9-0205-67e9-0000-000a0e5f4291
2025-05-29 03:30:53,866 - DEBUG - query execution done
2025-05-29 03:30:53,866 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002043', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8a9-0205-67e9-0000-000a0e5f4291', 'line': -1, 'pos': -1, 'type': 'COMPILATION'}, 'code': '002043', 'message': 'SQL compilation error:\nObject does not exist, or operation cannot be performed.', 'success': False, 'headers': None}
2025-05-29 03:30:53,866 - WARNING - Statement 3 failed: 002043 (02000): 01bca8a9-0205-67e9-0000-000a0e5f4291: SQL compilation error:  
Object does not exist, or operation cannot be performed.
2025-05-29 03:30:53,866 - DEBUG - Executing statement 4/12
2025-05-29 03:30:53,866 - DEBUG - executing SQL/command
2025-05-29 03:30:53,866 - DEBUG - query: [USE SCHEMA salesgenie_ai.analytics]
2025-05-29 03:30:53,866 - DEBUG - binding: [USE SCHEMA salesgenie_ai.analytics] with input=[None], processed=[{}]
2025-05-29 03:30:53,866 - DEBUG - sequence counter: 29
2025-05-29 03:30:53,866 - DEBUG - Request id: d8e91d51-4846-43a2-b513-b35e97ce3782
2025-05-29 03:30:53,866 - DEBUG - running query [USE SCHEMA salesgenie_ai.analytics]
2025-05-29 03:30:53,866 - DEBUG - is_file_transfer: True
2025-05-29 03:30:53,866 - DEBUG - _cmd_query
2025-05-29 03:30:53,866 - DEBUG - serialize_to_dict() called       
2025-05-29 03:30:53,866 - DEBUG - Cache Entry: (0, 1748469662994415, 0)
2025-05-29 03:30:53,866 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748469662994415, 'priority': 0, 'context': {'base64Data': 'CLrH1oBw'}}]}
2025-05-29 03:30:53,866 - DEBUG - sql=[USE SCHEMA salesgenie_ai.analytics], sequence_id=[29], is_file_transfer=[False]
2025-05-29 03:30:53,866 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 03:30:53,866 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:53,866 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 03:30:53,866 - DEBUG - Request guid: acea6212-8b42-4332-b81e-581dee133c3f
2025-05-29 03:30:53,866 - DEBUG - socket timeout: 60
2025-05-29 03:30:54,299 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=d8e91d51-4846-43a2-b513-b35e97ce3782&request_guid=acea6212-8b42-4332-b81e-581dee133c3f HTTP/1.1" 200 None
2025-05-29 03:30:54,299 - DEBUG - SUCCESS
2025-05-29 03:30:54,299 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:54,299 - DEBUG - ret[code] = 002043, after post request
2025-05-29 03:30:54,299 - DEBUG - Query id: 01bca8a9-0205-6eec-0000-000a0e5ff011
2025-05-29 03:30:54,299 - DEBUG - sfqid: 01bca8a9-0205-6eec-0000-000a0e5ff011
2025-05-29 03:30:54,299 - DEBUG - query execution done
2025-05-29 03:30:54,299 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002043', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8a9-0205-6eec-0000-000a0e5ff011', 'line': -1, 'pos': -1, 'type': 'COMPILATION'}, 'code': '002043', 'message': 'SQL compilation error:\nObject does not exist, or operation cannot be performed.', 'success': False, 'headers': None}
2025-05-29 03:30:54,309 - WARNING - Statement 4 failed: 002043 (02000): 01bca8a9-0205-6eec-0000-000a0e5ff011: SQL compilation error:  
Object does not exist, or operation cannot be performed.
2025-05-29 03:30:54,309 - DEBUG - Executing statement 12/12        
2025-05-29 03:30:54,311 - DEBUG - executing SQL/command
2025-05-29 03:30:54,311 - DEBUG - query: [COMMIT]
2025-05-29 03:30:54,311 - DEBUG - binding: [COMMIT] with input=[None], processed=[{}]
2025-05-29 03:30:54,311 - DEBUG - sequence counter: 30
2025-05-29 03:30:54,311 - DEBUG - Request id: 0cc26a66-1a07-4bce-8630-d40c8bbfb204
2025-05-29 03:30:54,311 - DEBUG - running query [COMMIT]
2025-05-29 03:30:54,313 - DEBUG - is_file_transfer: True
2025-05-29 03:30:54,313 - DEBUG - _cmd_query
2025-05-29 03:30:54,314 - DEBUG - serialize_to_dict() called       
2025-05-29 03:30:54,314 - DEBUG - Cache Entry: (0, 1748469662994415, 0)
2025-05-29 03:30:54,314 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748469662994415, 'priority': 0, 'context': {'base64Data': 'CLrH1oBw'}}]}
2025-05-29 03:30:54,315 - DEBUG - sql=[COMMIT], sequence_id=[30], is_file_transfer=[False]
2025-05-29 03:30:54,316 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 03:30:54,316 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:54,316 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 03:30:54,318 - DEBUG - Request guid: e6bc2574-d397-4897-97f6-df803da81357
2025-05-29 03:30:54,318 - DEBUG - socket timeout: 60
2025-05-29 03:30:54,709 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=0cc26a66-1a07-4bce-8630-d40c8bbfb204&request_guid=e6bc2574-d397-4897-97f6-df803da81357 HTTP/1.1" 200 None
2025-05-29 03:30:54,709 - DEBUG - SUCCESS
2025-05-29 03:30:54,709 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:54,709 - DEBUG - ret[code] = None, after post request
2025-05-29 03:30:54,709 - DEBUG - Query id: 01bca8a9-0205-696d-0000-000a0e5fa159
2025-05-29 03:30:54,709 - DEBUG - deserialize_json_dict() called: data from server: {'entries': [{'id': 0, 'timestamp': 1748469664738090, 'priority': 0, 'context': 'CLbL1oBw'}]}
2025-05-29 03:30:54,709 - DEBUG - Cache Entry: (0, 1748469662994415, 0)
2025-05-29 03:30:54,709 - DEBUG - deserialize {'id': 0, 'timestamp': 1748469664738090, 'priority': 0, 'context': 'CLbL1oBw'}
2025-05-29 03:30:54,709 - DEBUG - sync_priority_map called priority_map size = 0, new_priority_map size = 1
2025-05-29 03:30:54,709 - DEBUG - trim_cache() called. treeSet size is 1 and cache capacity is 5
2025-05-29 03:30:54,709 - DEBUG - trim_cache() returns. treeSet size is 1 and cache capacity is 5
2025-05-29 03:30:54,709 - DEBUG - deserialize_json_dict() returns  
2025-05-29 03:30:54,709 - DEBUG - Cache Entry: (0, 1748469664738090, 0)
2025-05-29 03:30:54,709 - DEBUG - sfqid: 01bca8a9-0205-696d-0000-000a0e5fa159
2025-05-29 03:30:54,709 - DEBUG - query execution done
2025-05-29 03:30:54,709 - DEBUG - SUCCESS
2025-05-29 03:30:54,709 - DEBUG - PUT OR GET: False
2025-05-29 03:30:54,709 - DEBUG - Query result format: json        
2025-05-29 03:30:54,709 - DEBUG - parsing for result batch id: 1   
2025-05-29 03:30:54,709 - DEBUG - Number of results in first chunk: 1
2025-05-29 03:30:54,709 - DEBUG - Statement executed successfully  
2025-05-29 03:30:54,709 - INFO - Successfully executed SQL file: sql/04_advanced_analytics.sql
2025-05-29 03:30:54,709 - INFO - Uploading semantic model...       
2025-05-29 03:30:54,709 - DEBUG - cursor
2025-05-29 03:30:54,709 - DEBUG - executing SQL/command
2025-05-29 03:30:54,709 - DEBUG - query: [CREATE STAGE IF NOT EXISTS salesgenie_ai.ai_services.semantic_models DIRECTORY =...]        
2025-05-29 03:30:54,709 - DEBUG - binding: [CREATE STAGE IF NOT EXISTS salesgenie_ai.ai_services.semantic_models DIRECTORY =...] with input=[None], processed=[{}]
2025-05-29 03:30:54,709 - DEBUG - sequence counter: 31
2025-05-29 03:30:54,709 - DEBUG - Request id: addd9621-6724-4e19-9c6e-17de3d1fca9b
2025-05-29 03:30:54,709 - DEBUG - running query [CREATE STAGE IF NOT EXISTS salesgenie_ai.ai_services.semantic_models DIRECTORY =...] 
2025-05-29 03:30:54,709 - DEBUG - is_file_transfer: True
2025-05-29 03:30:54,709 - DEBUG - _cmd_query
2025-05-29 03:30:54,717 - DEBUG - serialize_to_dict() called       
2025-05-29 03:30:54,717 - DEBUG - Cache Entry: (0, 1748469664738090, 0)
2025-05-29 03:30:54,718 - DEBUG - serialize_to_dict(): data to send to server {'entries': [{'id': 0, 'timestamp': 1748469664738090, 'priority': 0, 'context': {'base64Data': 'CLbL1oBw'}}]}
2025-05-29 03:30:54,718 - DEBUG - sql=[CREATE STAGE IF NOT EXISTS salesgenie_ai.ai_services.semantic_models DIRECTORY =...], sequence_id=[31], is_file_transfer=[False]
2025-05-29 03:30:54,718 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 03:30:54,718 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:54,718 - DEBUG - remaining request timeout: N/A ms, retry cnt: 1
2025-05-29 03:30:54,718 - DEBUG - Request guid: d9701256-5f8e-40dc-b4f5-05d44d520fb2
2025-05-29 03:30:54,718 - DEBUG - socket timeout: 60
2025-05-29 03:30:55,059 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /queries/v1/query-request?requestId=addd9621-6724-4e19-9c6e-17de3d1fca9b&request_guid=d9701256-5f8e-40dc-b4f5-05d44d520fb2 HTTP/1.1" 200 None
2025-05-29 03:30:55,073 - DEBUG - SUCCESS
2025-05-29 03:30:55,073 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:55,073 - DEBUG - ret[code] = 002003, after post request
2025-05-29 03:30:55,073 - DEBUG - Query id: 01bca8a9-0205-67e9-0000-000a0e5f4295
2025-05-29 03:30:55,073 - DEBUG - sfqid: 01bca8a9-0205-67e9-0000-000a0e5f4295
2025-05-29 03:30:55,073 - DEBUG - query execution done
2025-05-29 03:30:55,073 - DEBUG - {'data': {'internalError': False, 'unredactedFromSecureObject': False, 'errorCode': '002003', 'age': 0, 'sqlState': '02000', 'queryId': '01bca8a9-0205-67e9-0000-000a0e5f4295', 'line': 1, 'pos': 27, 'type': 'COMPILATION'}, 'code': '002003', 'message': "SQL compilation error:\nDatabase 'SALESGENIE_AI' does not exist or not authorized.", 'success': False, 'headers': None}
2025-05-29 03:30:55,076 - ERROR - Failed to upload semantic model: 002003 (02000): 01bca8a9-0205-67e9-0000-000a0e5f4295: SQL compilation error:
Database 'SALESGENIE_AI' does not exist or not authorized.
2025-05-29 03:30:55,077 - ERROR - Semantic model upload failed     
2025-05-29 03:30:55,077 - DEBUG - closed
2025-05-29 03:30:55,077 - DEBUG - Closing telemetry client.        
2025-05-29 03:30:55,081 - DEBUG - Sending 1 logs to telemetry. Data is {'logs': [{'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_imported_packages', 'value': "{'nt', 'gc', 'reprlib', 'cryptography', 'warnings', 'difflib', 'operator', 'tomlkit', 'http', 'random', 'genericpath', 'functools', 'copy', 'bisect', 'winreg', 'sys', 'math', 'numbers', 'mimetypes', 'dateutil', 'queue', 'msvcrt', 'typing_extensions', 'collections', 'traceback', 'asyncio', 'six', 'csv', 'bz2', 'tokenize', 'inspect', 'keyword', 'zipimport', 'hmac', 'struct', 'dataclasses', 'nturl2path', 'subprocess', 'linecache', 'sre_compile', 'html', 'urllib3', 'datetime', 'pytz', 'base64', 'importlib', 'ssl', 're', 'hashlib', 'calendar', 'botocore', 'errno', 'lzma', 'sre_parse', 'charset_normalizer', 'idna', 'uu', 'shutil', 'gettext', 'locale', 'pickle', 'token', 'ntpath', 'asn1crypto', 'signal', 'shlex', 'fnmatch', 'decimal', 'unicodedata', 'contextvars', 'itertools', 'contextlib', 'quopri', 'os', 'zipfile', 'pyexpat', 'string', 'packaging', 'atexit', 'time', 'ipaddress', 'configparser', 'select', 'site', 'heapq', 'weakref', 'logging', 'snowflake', 'socket', 'posixpath', 'platformdirs', 'argparse', 'pathlib', 'codecs', 'ctypes', 'builtins', 'concurrent', 'textwrap', 'stringprep', 'io', 'OpenSSL', 'binascii', 'ast', 'abc', 'cython_runtime', 'email', 'sre_constants', 'requests', 'sortedcontainers', 'enum', 'zlib', 'tempfile', 'jwt', 'boto3', 'opcode', 'secrets', 'threading', 'platform', 'sysconfig', 'json', 'jmespath', 'gzip', 'xml', 'dis', 'getpass', 'stat', 'selectors', 'filelock', 'fractions', 'copyreg', 'urllib', 'uuid', 'typing', 'certifi', 'types', 'marshal', 'encodings', 'webbrowser'}"}, 'timestamp': '1748469641130'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-6cf8-0000-000a0e5fd005', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469641698'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8a8-0205-68ee-0000-000a0e5f82ed', 'value': -10058}, 'timestamp': '1748469642223'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-67fa-0000-000a0e5f9449', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469642669'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-67e9-0000-000a0e5f4285', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469643125'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-6cf9-0000-000a0e5fe005', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469643655'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-6cf8-0000-000a0e5fd009', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469644056'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-68ee-0000-000a0e5f82f1', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469644435'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-67fa-0000-000a0e5f944d', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469644802'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-67e9-0000-000a0e5f4289', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469645184'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-6cf9-0000-000a0e5fe009', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469645550'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-6eec-0000-000a0e5ff005', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469645993'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-696d-0000-000a0e5fa14d', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469646505'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-6cf8-0000-000a0e5fd00d', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469646934'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-68ee-0000-000a0e5f82f5', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469647436'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-67fa-0000-000a0e5f9451', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469647958'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-6eec-0000-000a0e5ff009', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469648366'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-696d-0000-000a0e5fa151', 'sql_state': '22000', 'reason': '090105 (22000)', 'ErrorNumber': '90105', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469648881'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8a8-0205-6cf8-0000-000a0e5fd011', 'value': -10059}, 'timestamp': '1748469649289'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a8-0205-68ee-0000-000a0e5f82f9', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469649799'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a9-0205-67e9-0000-000a0e5f428d', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469650171'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a9-0205-6ab1-0000-000a0e5f7211', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469650827'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8a9-0205-6cf9-0000-000a0e5fe00d', 'value': -10009}, 'timestamp': '1748469651337'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8a9-0205-6eec-0000-000a0e5ff00d', 'value': -10033}, 'timestamp': '1748469651748'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a9-0205-696d-0000-000a0e5fa155', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469652150'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a9-0205-6cf8-0000-000a0e5fd015', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469652563'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8a9-0205-68ee-0000-000a0e5f82fd', 'value': -10018}, 'timestamp': '1748469652988'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a9-0205-67fa-0000-000a0e5f9455', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469653483'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a9-0205-67e9-0000-000a0e5f4291', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469653866'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a9-0205-6eec-0000-000a0e5ff011', 'sql_state': '02000', 'reason': '002043 (02000)', 'ErrorNumber': '2043', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469654309'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'type': 'client_time_consume_first_result', 'query_id': '01bca8a9-0205-696d-0000-000a0e5fa159', 'value': -10035}, 'timestamp': '1748469654709'}, {'message': {'driver_type': 'PythonConnector', 'driver_version': '3.15.0', 'source': 'PythonConnector', 'Stacktrace': MaskedMessageData(is_masked=False, masked_text='  File "snowflake\\connector\\cursor.py", line 1134, in execute\n  File "snowflake\\connector\\errors.py", line 279, in errorhandler_wrapper\n  File "snowflake\\connector\\errors.py", line 334, in hand_to_other_handler\n  File "snowflake\\connector\\errors.py", line 210, in default_errorhandler\n', error_str=None), 'query_id': '01bca8a9-0205-67e9-0000-000a0e5f4295', 'sql_state': '02000', 'reason': '002003 (02000)', 'ErrorNumber': '2003', 'type': 'client_sql_exception', 'exception': 'ProgrammingError'}, 'timestamp': '1748469655076'}]}.
2025-05-29 03:30:55,081 - DEBUG - Opentelemtry otel injection failed
Traceback (most recent call last):
  File "C:\Users\<USER>\NihilentXSnowflakeenv\lib\site-packages\snowflake\connector\network.py", line 498, in request     
    from opentelemetry.trace.propagation.tracecontext import (     
ModuleNotFoundError: No module named 'opentelemetry'
2025-05-29 03:30:55,081 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:55,081 - DEBUG - remaining request timeout: 5000 ms, retry cnt: 1
2025-05-29 03:30:55,081 - DEBUG - Request guid: ac0beb05-87fe-4c41-9f6d-33fed2b36b98
2025-05-29 03:30:55,081 - DEBUG - socket timeout: 60
2025-05-29 03:30:55,426 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /telemetry/send?request_guid=ac0beb05-87fe-4c41-9f6d-33fed2b36b98 HTTP/1.1" 200 None
2025-05-29 03:30:55,429 - DEBUG - SUCCESS
2025-05-29 03:30:55,429 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:55,429 - DEBUG - ret[code] = None, after post request
2025-05-29 03:30:55,429 - DEBUG - Successfully uploading metrics to telemetry.
2025-05-29 03:30:55,429 - DEBUG - No async queries seem to be running, deleting session
2025-05-29 03:30:55,429 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 1/1 active sessions
2025-05-29 03:30:55,429 - DEBUG - remaining request timeout: 5000 ms, retry cnt: 1
2025-05-29 03:30:55,429 - DEBUG - Request guid: 18ad6481-dcb9-4645-b5f5-b1365429955a
2025-05-29 03:30:55,429 - DEBUG - socket timeout: 60
2025-05-29 03:30:55,825 - DEBUG - https://gnbxjlf-pkb97538.snowflakecomputing.com:443 "POST /session?delete=true&request_guid=18ad6481-dcb9-4645-b5f5-b1365429955a HTTP/1.1" 200 None
2025-05-29 03:30:55,827 - DEBUG - SUCCESS
2025-05-29 03:30:55,827 - DEBUG - Session status for SessionPool 'gnbxjlf-pkb97538.snowflakecomputing.com', SessionPool 0/1 active sessions
2025-05-29 03:30:55,827 - DEBUG - ret[code] = None, after post request
2025-05-29 03:30:55,831 - DEBUG - clear_cache() called
2025-05-29 03:30:55,831 - DEBUG - Session is closed

============================================================       
❌ DEPLOYMENT FAILED
============================================================       

Please check the logs above for error details.
Common issues:
- Incorrect Snowflake credentials
- Insufficient privileges
- Network connectivity issues