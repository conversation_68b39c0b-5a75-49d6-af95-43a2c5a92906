# 🚀 SalesGenie AI - Deployment Guide

## Prerequisites

### Snowflake Requirements
- Snowflake account with Cortex AI enabled
- Account admin privileges or appropriate roles
- Available compute credits for AI services
- Supported region (see [Cortex availability](https://docs.snowflake.com/en/user-guide/snowflake-cortex/llm-functions#availability))

### Local Environment
- Python 3.9 or higher
- Git for version control
- Internet connection for package downloads

## Step-by-Step Deployment

### 1. <PERSON><PERSON> and Setup

```bash
# Clone the repository
git clone <repository-url>
cd salesgenie-ai

# Install Python dependencies
pip install -r requirements.txt
```

### 2. Configure Snowflake Connection

```bash
# Copy configuration template
cp config/snowflake_config.template.json config/snowflake_config.json

# Edit with your Snowflake credentials
nano config/snowflake_config.json
```

**Required Configuration:**
```json
{
  "account": "your_account.region",
  "user": "your_username",
  "password": "your_password",
  "warehouse": "salesgenie_ai_wh",
  "database": "salesgenie_ai",
  "schema": "crm_data",
  "role": "salesgenie_ai_role"
}
```

### 3. Deploy to Snowflake

```bash
# Run deployment script
python scripts/deploy_to_snowflake.py --config config/snowflake_config.json --verbose
```

The deployment script will:
1. ✅ Create database, schemas, and warehouses
2. ✅ Set up user roles and privileges
3. ✅ Create CRM data tables
4. ✅ Load sample data
5. ✅ Configure Cortex AI services
6. ✅ Set up Cortex Search
7. ✅ Deploy semantic model
8. ✅ Create analytics views
9. ✅ Deploy Streamlit application

### 4. Access the Application

1. Log into your Snowflake account
2. Navigate to **Streamlit** in the left sidebar
3. Find **SalesGenie AI** application
4. Click to launch the conversational interface

## Manual Deployment (Alternative)

If you prefer manual deployment:

### 1. Database Setup
```sql
-- Execute in Snowflake worksheet
@sql/01_setup_database.sql
```

### 2. Sample Data
```sql
-- Load sample CRM data
@sql/02_sample_data.sql
```

### 3. AI Services
```sql
-- Configure Cortex AI services
@sql/03_ai_services_setup.sql
```

### 4. Advanced Analytics
```sql
-- Set up ML models and analytics
@sql/04_advanced_analytics.sql
```

### 5. Semantic Model
1. Upload `semantic_models/sales_crm_semantic_model.yaml` to stage
2. Configure Cortex Analyst to use the model

### 6. Streamlit App
1. Create new Streamlit app in Snowflake
2. Copy content from `streamlit_app.py`
3. Configure app settings

## Verification

### Test Basic Functionality
```sql
-- Check data loading
SELECT COUNT(*) FROM salesgenie_ai.crm_data.companies;
SELECT COUNT(*) FROM salesgenie_ai.crm_data.opportunities;

-- Test AI functions
SELECT ai_lead_scoring(1000000, 'Technology', 'Enterprise (1000+)', 'Website', 'CTO');

-- Test analytics views
SELECT * FROM salesgenie_ai.analytics.sales_pipeline LIMIT 5;
```

### Test Conversational Interface
1. Open SalesGenie AI Streamlit app
2. Try sample questions:
   - "Show me my sales pipeline"
   - "Which leads need follow-up?"
   - "How is my team performing?"

## Troubleshooting

### Common Issues

**Connection Failed**
- Verify Snowflake credentials
- Check account URL format
- Ensure user has required privileges

**Cortex AI Not Available**
- Verify your region supports Cortex
- Check if Cortex is enabled for your account
- Contact Snowflake support if needed

**Permission Denied**
- Ensure user has ACCOUNTADMIN or appropriate roles
- Grant CORTEX_USER role to deployment user
- Check warehouse usage privileges

**Deployment Script Fails**
- Run with `--verbose` flag for detailed logs
- Check individual SQL files for syntax errors
- Verify all required files are present

### Getting Help

1. **Check Logs**: Deployment script provides detailed logging
2. **Snowflake Documentation**: [Cortex AI Documentation](https://docs.snowflake.com/en/user-guide/snowflake-cortex)
3. **Community Support**: Snowflake Community Forums
4. **Professional Support**: Contact Snowflake Support

## Configuration Options

### Cortex Models
Supported models for different use cases:
- **claude-3-5-sonnet**: Best for complex reasoning
- **llama3.1-70b**: Good balance of performance and cost
- **mistral-large2**: Excellent for code generation

### Performance Tuning
- **Warehouse Size**: Start with LARGE, scale as needed
- **Auto-suspend**: Set to 60 seconds for cost optimization
- **Cortex Search**: Configure TARGET_LAG based on data freshness needs

### Security Settings
- **Role-based Access**: Configure granular permissions
- **Data Masking**: Implement for sensitive fields
- **Network Policies**: Restrict access by IP if needed

## Next Steps

### Customization
1. **Add Your Data**: Replace sample data with real CRM data
2. **Customize Semantic Model**: Modify YAML for your schema
3. **Extend Analytics**: Add custom views and functions
4. **Brand the Interface**: Customize Streamlit app appearance

### Integration
1. **CRM Connectors**: Set up real-time data sync
2. **Email Integration**: Connect to email platforms
3. **Calendar Sync**: Integrate with calendar systems
4. **Mobile Access**: Configure mobile-friendly interface

### Monitoring
1. **Usage Analytics**: Track user interactions
2. **Performance Metrics**: Monitor query performance
3. **Cost Management**: Set up credit usage alerts
4. **Data Quality**: Implement data validation rules

---

**🎉 Congratulations! You've successfully deployed SalesGenie AI!**

Your sales team now has access to:
- ✅ Conversational AI interface for CRM data
- ✅ Intelligent lead scoring and prioritization
- ✅ Predictive sales analytics
- ✅ Automated workflow recommendations
- ✅ Real-time pipeline insights

Start exploring your sales data with natural language queries and watch your team's productivity soar! 🚀
