#!/usr/bin/env python3
"""
SalesGenie AI - Application Test Script
Nihilent x Snowflake Hackathon 2025

Simple test script to verify the Streamlit application works correctly.
"""

import sys
import os
import subprocess
import time

def test_imports():
    """Test that all required imports work"""
    print("Testing imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Streamlit import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ Pandas imported successfully")
    except ImportError as e:
        print(f"❌ Pandas import failed: {e}")
        return False
    
    try:
        import plotly.express as px
        print("✅ Plotly imported successfully")
    except ImportError as e:
        print(f"❌ Plotly import failed: {e}")
        return False
    
    # Test Snowflake imports (optional)
    try:
        import snowflake.connector
        import snowflake.snowpark
        print("✅ Snowflake packages imported successfully")
    except ImportError as e:
        print(f"⚠️ Snowflake packages not available: {e}")
        print("   This is OK for demo mode")
    
    return True

def test_file_structure():
    """Test that all required files exist"""
    print("\nTesting file structure...")
    
    required_files = [
        "streamlit_app.py",
        "README.md",
        "requirements.txt",
        "sql/01_setup_database.sql",
        "sql/02_sample_data.sql",
        "sql/03_ai_services_setup.sql",
        "sql/04_advanced_analytics.sql",
        "semantic_models/sales_crm_semantic_model.yaml",
        "scripts/deploy_to_snowflake.py",
        "config/snowflake_config.template.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required files present")
    return True

def test_streamlit_syntax():
    """Test that the Streamlit app has valid syntax"""
    print("\nTesting Streamlit app syntax...")
    
    try:
        # Try to compile the streamlit app
        with open("streamlit_app.py", "r") as f:
            code = f.read()
        
        compile(code, "streamlit_app.py", "exec")
        print("✅ Streamlit app syntax is valid")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error in streamlit_app.py: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking streamlit_app.py: {e}")
        return False

def test_demo_mode():
    """Test that demo mode works without Snowflake connection"""
    print("\nTesting demo mode functionality...")
    
    try:
        # Import the main functions from the app
        sys.path.append(".")
        
        # Test that we can import the app module
        import streamlit_app
        print("✅ App module imported successfully")
        
        # Test demo response generation
        messages = [{"role": "user", "content": "show me pipeline"}]
        response, error = streamlit_app.get_analyst_response(messages)
        
        if error:
            print(f"❌ Demo response failed: {error}")
            return False
        
        if response and "message" in response:
            print("✅ Demo responses working")
            return True
        else:
            print("❌ Demo response format invalid")
            return False
            
    except Exception as e:
        print(f"❌ Demo mode test failed: {e}")
        return False

def run_streamlit_test():
    """Try to run Streamlit app for a few seconds"""
    print("\nTesting Streamlit app startup...")
    
    try:
        # Start Streamlit in background
        process = subprocess.Popen(
            ["streamlit", "run", "streamlit_app.py", "--server.headless", "true", "--server.port", "8502"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a few seconds
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Streamlit app started successfully")
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Streamlit app failed to start")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return False
            
    except FileNotFoundError:
        print("⚠️ Streamlit not found in PATH. Install with: pip install streamlit")
        return False
    except Exception as e:
        print(f"❌ Streamlit test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 SalesGenie AI - Application Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("File Structure Test", test_file_structure),
        ("Syntax Test", test_streamlit_syntax),
        ("Demo Mode Test", test_demo_mode),
        ("Streamlit Startup Test", run_streamlit_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! SalesGenie AI is ready for deployment.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
