graph TB
    %% Problem Statement
    subgraph Problem["🚨 Current Problems"]
        P1["⏰ Time-consuming CRM data entry"]
        P2["📊 Limited real-time insights"]
        P3["📉 Reduced productivity"]
    end

    %% Solution Core
    subgraph Solution["🤖 Conversational AI Chatbot Solution"]
        subgraph Snowflake["❄️ Snowflake Platform"]
            SC["Snowflake Cortex<br/>🧠 AI/ML Engine"]
            SP["Snowpark Python<br/>🐍 Development"]
            DB["Snowflake DB<br/>🗄️ Data Integration"]
        end
        
        subgraph Features["🎯 Core Features"]
            F1["🔄 Automated CRM Updates"]
            F2["💬 Natural Language Querying"]
            F3["🔮 Predictive Lead Generation"]
            F4["💰 Dynamic Pricing"]
            F5["🌍 Multilingual Support"]
            F6["🚨 Anomaly Detection"]
            F7["💡 New Lead Suggestions"]
            F8["📧 Personalized Follow-ups"]
            F9["🔄 Feedback Loop"]
        end
    end

    %% External Systems
    subgraph External["🔗 External Systems"]
        CRM["CRM System<br/>📋 Customer Data"]
        Sales["Sales Teams<br/>👥 End Users"]
    end

    %% Benefits
    subgraph Benefits["✅ Business Benefits"]
        B1["📈 Higher Productivity"]
        B2["⚡ Reduced Workload"]
        B3["🎯 Improved Decision-Making"]
        B4["📊 Higher Conversion Rates"]
        B5["🚀 Business Efficiency"]
        B6["👤 User-Friendly Interface"]
    end

    %% Connections
    Problem --> Solution
    Sales --> Solution
    Solution <--> CRM
    
    %% Internal connections
    SC --> F1
    SC --> F2
    SC --> F3
    SP --> F4
    SP --> F5
    DB --> F6
    DB --> F7
    SC --> F8
    SP --> F9
    
    %% Benefits flow
    F1 --> B1
    F2 --> B3
    F3 --> B4
    F4 --> B5
    F5 --> B6
    F6 --> B3
    F7 --> B4
    F8 --> B4
    F9 --> B2

    %% Styling
    classDef problemStyle fill:#ffebee,stroke:#f44336,stroke-width:2px
    classDef solutionStyle fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef benefitStyle fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef snowflakeStyle fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef featureStyle fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    
    class Problem,P1,P2,P3 problemStyle
    class Solution,Features,F1,F2,F3,F4,F5,F6,F7,F8,F9 solutionStyle
    class Benefits,B1,B2,B3,B4,B5,B6 benefitStyle
    class Snowflake,SC,SP,DB snowflakeStyle
    class External,CRM,Sales featureStyle
