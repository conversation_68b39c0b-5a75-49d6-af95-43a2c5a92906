/*
SalesGenie AI - Advanced Analytics & ML Models
Nihilent x Snowflake Hackathon 2025

This script creates advanced analytics views, ML models, and predictive functions
for sales forecasting, churn prediction, and intelligent recommendations.
*/

USE ROLE ACCOUNTADMIN;
USE WAREHOUSE salesgenie_ai_wh;
USE DATABASE salesgenie_ai;
USE SCHEMA salesgenie_ai.analytics;

-- =====================================================
-- ANALYTICS SCHEMA SETUP
-- =====================================================

-- Create analytics tables for historical data and ML models
CREATE OR REPLACE TABLE sales_forecasts (
    forecast_id STRING PRIMARY KEY,
    forecast_date DATE,
    forecast_period STRING, -- 'monthly', 'quarterly', 'yearly'
    territory STRING,
    sales_rep STRING,
    predicted_revenue NUMBER(15,2),
    confidence_interval_low NUMBER(15,2),
    confidence_interval_high NUMBER(15,2),
    model_version STRING,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Historical performance metrics
CREATE OR REPLACE TABLE historical_metrics (
    metric_id STRING PRIMARY KEY,
    metric_date DATE,
    metric_type STRING, -- 'conversion_rate', 'win_rate', 'avg_deal_size', etc.
    territory STRING,
    sales_rep STRING,
    metric_value NUMBER(10,4),
    benchmark_value NUMBER(10,4),
    variance_percent NUMBER(6,2),
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Customer churn predictions
CREATE OR REPLACE TABLE churn_predictions (
    prediction_id STRING PRIMARY KEY,
    company_id STRING,
    churn_probability NUMBER(5,4), -- 0.0 to 1.0
    churn_risk_level STRING, -- 'Low', 'Medium', 'High', 'Critical'
    key_risk_factors VARIANT,
    recommended_actions VARIANT,
    prediction_date DATE,
    model_confidence NUMBER(5,4),
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Lead scoring models
CREATE OR REPLACE TABLE lead_scoring_models (
    model_id STRING PRIMARY KEY,
    model_name STRING,
    model_type STRING, -- 'logistic_regression', 'random_forest', 'neural_network'
    model_parameters VARIANT,
    training_data_period STRING,
    accuracy_score NUMBER(5,4),
    precision_score NUMBER(5,4),
    recall_score NUMBER(5,4),
    f1_score NUMBER(5,4),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
    updated_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- Sales performance benchmarks
CREATE OR REPLACE TABLE performance_benchmarks (
    benchmark_id STRING PRIMARY KEY,
    benchmark_type STRING, -- 'industry', 'company', 'territory', 'role'
    benchmark_category STRING,
    metric_name STRING,
    benchmark_value NUMBER(15,4),
    percentile_25 NUMBER(15,4),
    percentile_50 NUMBER(15,4),
    percentile_75 NUMBER(15,4),
    percentile_90 NUMBER(15,4),
    data_source STRING,
    effective_date DATE,
    created_date TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP()
);

-- =====================================================
-- PREDICTIVE ANALYTICS VIEWS
-- =====================================================

-- Sales forecasting view
CREATE OR REPLACE VIEW sales_forecast AS
WITH monthly_sales AS (
    SELECT
        DATE_TRUNC('month', o.created_date) AS month,
        u.territory,
        CONCAT(u.first_name, ' ', u.last_name) AS sales_rep,
        COUNT(o.opportunity_id) AS total_opportunities,
        COUNT(CASE WHEN o.is_won = TRUE THEN 1 END) AS won_opportunities,
        SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) AS actual_revenue,
        AVG(o.amount) AS avg_deal_size
    FROM salesgenie_ai.crm_data.opportunities o
    JOIN salesgenie_ai.crm_data.sales_users u ON o.sales_rep = u.user_id
    WHERE o.created_date >= DATEADD('month', -12, CURRENT_DATE())
    GROUP BY DATE_TRUNC('month', o.created_date), u.territory, u.first_name, u.last_name
),
trend_analysis AS (
    SELECT
        territory,
        sales_rep,
        AVG(actual_revenue) AS avg_monthly_revenue,
        STDDEV(actual_revenue) AS revenue_volatility,
        COUNT(*) AS months_of_data,
        -- Simple linear trend calculation
        REGR_SLOPE(actual_revenue, EXTRACT(EPOCH FROM month)) AS revenue_trend
    FROM monthly_sales
    GROUP BY territory, sales_rep
)
SELECT
    t.territory,
    t.sales_rep,
    t.avg_monthly_revenue,
    t.revenue_volatility,
    t.revenue_trend,
    -- Forecast next month (simplified)
    GREATEST(0, t.avg_monthly_revenue + (t.revenue_trend * 2629746)) AS next_month_forecast,
    -- Confidence intervals
    GREATEST(0, t.avg_monthly_revenue - t.revenue_volatility) AS forecast_low,
    t.avg_monthly_revenue + t.revenue_volatility AS forecast_high,
    CASE
        WHEN t.revenue_trend > 0 THEN 'Growing'
        WHEN t.revenue_trend < -1000 THEN 'Declining'
        ELSE 'Stable'
    END AS trend_direction
FROM trend_analysis t;

-- Customer health score view
CREATE OR REPLACE VIEW customer_health_score AS
WITH customer_metrics AS (
    SELECT
        c.company_id,
        c.company_name,
        c.industry,
        c.annual_revenue,
        -- Opportunity metrics
        COUNT(o.opportunity_id) AS total_opportunities,
        COUNT(CASE WHEN o.is_won = TRUE THEN 1 END) AS won_opportunities,
        COUNT(CASE WHEN o.is_lost = TRUE THEN 1 END) AS lost_opportunities,
        SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) AS total_revenue,
        MAX(o.created_date) AS last_opportunity_date,
        -- Activity metrics
        COUNT(a.activity_id) AS total_activities,
        MAX(a.activity_date) AS last_activity_date,
        AVG(COALESCE(SNOWFLAKE.CORTEX.SENTIMENT(a.description), 0)) AS avg_sentiment
    FROM salesgenie_ai.crm_data.companies c
    LEFT JOIN salesgenie_ai.crm_data.opportunities o ON c.company_id = o.company_id
    LEFT JOIN salesgenie_ai.crm_data.activities a ON c.company_id = a.company_id
    GROUP BY c.company_id, c.company_name, c.industry, c.annual_revenue
)
SELECT
    company_id,
    company_name,
    industry,
    annual_revenue,
    total_opportunities,
    won_opportunities,
    total_revenue,
    DATEDIFF('day', last_activity_date, CURRENT_DATE()) AS days_since_last_contact,
    avg_sentiment,
    -- Health score calculation (0-100)
    LEAST(100, GREATEST(0,
        50 + -- Base score
        (CASE WHEN won_opportunities > 0 THEN 20 ELSE 0 END) + -- Has won deals
        (CASE WHEN total_revenue > 50000 THEN 15 ELSE total_revenue/50000*15 END) + -- Revenue contribution
        (CASE WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) < 30 THEN 10 ELSE 0 END) + -- Recent contact
        (avg_sentiment * 5) - -- Sentiment impact
        (CASE WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) > 90 THEN 20 ELSE 0 END) -- Penalty for no recent contact
    )) AS health_score,
    CASE
        WHEN LEAST(100, GREATEST(0, 50 + (CASE WHEN won_opportunities > 0 THEN 20 ELSE 0 END) + (CASE WHEN total_revenue > 50000 THEN 15 ELSE total_revenue/50000*15 END) + (CASE WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) < 30 THEN 10 ELSE 0 END) + (avg_sentiment * 5) - (CASE WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) > 90 THEN 20 ELSE 0 END))) >= 80 THEN 'Excellent'
        WHEN LEAST(100, GREATEST(0, 50 + (CASE WHEN won_opportunities > 0 THEN 20 ELSE 0 END) + (CASE WHEN total_revenue > 50000 THEN 15 ELSE total_revenue/50000*15 END) + (CASE WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) < 30 THEN 10 ELSE 0 END) + (avg_sentiment * 5) - (CASE WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) > 90 THEN 20 ELSE 0 END))) >= 60 THEN 'Good'
        WHEN LEAST(100, GREATEST(0, 50 + (CASE WHEN won_opportunities > 0 THEN 20 ELSE 0 END) + (CASE WHEN total_revenue > 50000 THEN 15 ELSE total_revenue/50000*15 END) + (CASE WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) < 30 THEN 10 ELSE 0 END) + (avg_sentiment * 5) - (CASE WHEN DATEDIFF('day', last_activity_date, CURRENT_DATE()) > 90 THEN 20 ELSE 0 END))) >= 40 THEN 'At Risk'
        ELSE 'Critical'
    END AS health_category
FROM customer_metrics;

-- =====================================================
-- SAMPLE DATA FOR ANALYTICS TABLES
-- =====================================================

-- Insert sample sales forecasts
INSERT INTO sales_forecasts (forecast_id, forecast_date, forecast_period, territory, sales_rep, predicted_revenue, confidence_interval_low, confidence_interval_high, model_version) VALUES
('forecast_001', '2025-02-01', 'monthly', 'West Coast', 'user_002', 125000.00, 95000.00, 155000.00, 'v1.2'),
('forecast_002', '2025-02-01', 'monthly', 'East Coast', 'user_003', 110000.00, 85000.00, 135000.00, 'v1.2'),
('forecast_003', '2025-02-01', 'monthly', 'Central', 'user_004', 95000.00, 70000.00, 120000.00, 'v1.2'),
('forecast_004', '2025-03-01', 'monthly', 'West Coast', 'user_002', 135000.00, 105000.00, 165000.00, 'v1.2'),
('forecast_005', '2025-03-01', 'monthly', 'East Coast', 'user_003', 120000.00, 95000.00, 145000.00, 'v1.2'),
('forecast_006', '2025-01-01', 'quarterly', 'West Coast', 'user_002', 375000.00, 300000.00, 450000.00, 'v1.2'),
('forecast_007', '2025-01-01', 'quarterly', 'East Coast', 'user_003', 330000.00, 270000.00, 390000.00, 'v1.2'),
('forecast_008', '2025-01-01', 'quarterly', 'Central', 'user_004', 285000.00, 230000.00, 340000.00, 'v1.2');

-- Insert historical metrics
INSERT INTO historical_metrics (metric_id, metric_date, metric_type, territory, sales_rep, metric_value, benchmark_value, variance_percent) VALUES
('metric_001', '2024-12-01', 'conversion_rate', 'West Coast', 'user_002', 0.2250, 0.2000, 12.50),
('metric_002', '2024-12-01', 'conversion_rate', 'East Coast', 'user_003', 0.1850, 0.2000, -7.50),
('metric_003', '2024-12-01', 'conversion_rate', 'Central', 'user_004', 0.1950, 0.2000, -2.50),
('metric_004', '2024-12-01', 'win_rate', 'West Coast', 'user_002', 0.3500, 0.3000, 16.67),
('metric_005', '2024-12-01', 'win_rate', 'East Coast', 'user_003', 0.2800, 0.3000, -6.67),
('metric_006', '2024-12-01', 'win_rate', 'Central', 'user_004', 0.3200, 0.3000, 6.67),
('metric_007', '2024-12-01', 'avg_deal_size', 'West Coast', 'user_002', 85000.00, 75000.00, 13.33),
('metric_008', '2024-12-01', 'avg_deal_size', 'East Coast', 'user_003', 92000.00, 75000.00, 22.67),
('metric_009', '2024-12-01', 'avg_deal_size', 'Central', 'user_004', 78000.00, 75000.00, 4.00),
('metric_010', '2024-11-01', 'conversion_rate', 'West Coast', 'user_002', 0.2100, 0.2000, 5.00),
('metric_011', '2024-11-01', 'conversion_rate', 'East Coast', 'user_003', 0.1950, 0.2000, -2.50),
('metric_012', '2024-11-01', 'conversion_rate', 'Central', 'user_004', 0.2050, 0.2000, 2.50);

-- Insert churn predictions
INSERT INTO churn_predictions (prediction_id, company_id, churn_probability, churn_risk_level, key_risk_factors, recommended_actions, prediction_date, model_confidence) VALUES
('churn_001', 'comp_001', 0.1500, 'Low',
 PARSE_JSON('{"factors": ["recent_engagement", "high_satisfaction", "multiple_products"], "scores": {"engagement": 0.85, "satisfaction": 0.90, "product_adoption": 0.75}}'),
 PARSE_JSON('{"actions": ["maintain_regular_contact", "upsell_opportunities", "customer_success_check"], "priority": "low"}'),
 '2025-01-15', 0.8500),
('churn_002', 'comp_002', 0.3500, 'Medium',
 PARSE_JSON('{"factors": ["declining_usage", "support_tickets", "contract_renewal_approaching"], "scores": {"engagement": 0.45, "satisfaction": 0.60, "product_adoption": 0.55}}'),
 PARSE_JSON('{"actions": ["schedule_executive_meeting", "address_support_issues", "renewal_discussion"], "priority": "medium"}'),
 '2025-01-15', 0.7800),
('churn_003', 'comp_003', 0.7200, 'High',
 PARSE_JSON('{"factors": ["no_recent_contact", "missed_payments", "competitor_evaluation"], "scores": {"engagement": 0.20, "satisfaction": 0.35, "product_adoption": 0.30}}'),
 PARSE_JSON('{"actions": ["immediate_intervention", "executive_escalation", "retention_offer"], "priority": "critical"}'),
 '2025-01-15', 0.9200),
('churn_004', 'comp_004', 0.2200, 'Low',
 PARSE_JSON('{"factors": ["stable_usage", "positive_feedback", "expansion_interest"], "scores": {"engagement": 0.75, "satisfaction": 0.80, "product_adoption": 0.70}}'),
 PARSE_JSON('{"actions": ["expansion_discussion", "case_study_opportunity", "reference_program"], "priority": "low"}'),
 '2025-01-15', 0.8200),
('churn_005', 'comp_005', 0.4500, 'Medium',
 PARSE_JSON('{"factors": ["budget_constraints", "limited_adoption", "change_management"], "scores": {"engagement": 0.50, "satisfaction": 0.55, "product_adoption": 0.40}}'),
 PARSE_JSON('{"actions": ["training_program", "adoption_support", "value_demonstration"], "priority": "medium"}'),
 '2025-01-15', 0.7500);

-- Insert lead scoring models
INSERT INTO lead_scoring_models (model_id, model_name, model_type, model_parameters, training_data_period, accuracy_score, precision_score, recall_score, f1_score) VALUES
('model_001', 'Enterprise Lead Scorer v1.2', 'logistic_regression',
 PARSE_JSON('{"features": ["company_revenue", "industry", "company_size", "lead_source", "contact_title"], "weights": {"company_revenue": 0.35, "industry": 0.25, "company_size": 0.20, "lead_source": 0.15, "contact_title": 0.05}}'),
 '2024-Q4', 0.8750, 0.8200, 0.8900, 0.8540),
('model_002', 'SMB Lead Scorer v1.1', 'random_forest',
 PARSE_JSON('{"features": ["company_revenue", "industry", "lead_source", "engagement_score"], "n_estimators": 100, "max_depth": 10}'),
 '2024-Q4', 0.8200, 0.7800, 0.8500, 0.8140),
('model_003', 'Deep Learning Lead Scorer v2.0', 'neural_network',
 PARSE_JSON('{"architecture": "feedforward", "layers": [64, 32, 16, 1], "activation": "relu", "dropout": 0.2}'),
 '2024-Q4', 0.9100, 0.8800, 0.9200, 0.8990);

-- Insert performance benchmarks
INSERT INTO performance_benchmarks (benchmark_id, benchmark_type, benchmark_category, metric_name, benchmark_value, percentile_25, percentile_50, percentile_75, percentile_90, data_source, effective_date) VALUES
('bench_001', 'industry', 'Technology', 'lead_conversion_rate', 0.2000, 0.1500, 0.2000, 0.2500, 0.3000, 'Industry Report 2024', '2024-12-01'),
('bench_002', 'industry', 'Technology', 'win_rate', 0.3000, 0.2200, 0.3000, 0.3800, 0.4500, 'Industry Report 2024', '2024-12-01'),
('bench_003', 'industry', 'Technology', 'avg_deal_size', 75000.0000, 45000.0000, 75000.0000, 120000.0000, 200000.0000, 'Industry Report 2024', '2024-12-01'),
('bench_004', 'industry', 'Manufacturing', 'lead_conversion_rate', 0.1800, 0.1200, 0.1800, 0.2300, 0.2800, 'Industry Report 2024', '2024-12-01'),
('bench_005', 'industry', 'Manufacturing', 'win_rate', 0.2800, 0.2000, 0.2800, 0.3500, 0.4200, 'Industry Report 2024', '2024-12-01'),
('bench_006', 'industry', 'Healthcare', 'lead_conversion_rate', 0.2200, 0.1600, 0.2200, 0.2800, 0.3400, 'Industry Report 2024', '2024-12-01'),
('bench_007', 'company', 'SalesGenie_AI', 'lead_conversion_rate', 0.2100, 0.1800, 0.2100, 0.2400, 0.2700, 'Internal Analytics', '2024-12-01'),
('bench_008', 'company', 'SalesGenie_AI', 'win_rate', 0.3200, 0.2800, 0.3200, 0.3600, 0.4000, 'Internal Analytics', '2024-12-01'),
('bench_009', 'territory', 'West Coast', 'avg_deal_size', 85000.0000, 65000.0000, 85000.0000, 105000.0000, 130000.0000, 'Internal Analytics', '2024-12-01'),
('bench_010', 'territory', 'East Coast', 'avg_deal_size', 92000.0000, 70000.0000, 92000.0000, 115000.0000, 140000.0000, 'Internal Analytics', '2024-12-01'),
('bench_011', 'role', 'sales_rep', 'monthly_revenue', 95000.0000, 65000.0000, 95000.0000, 125000.0000, 160000.0000, 'Internal Analytics', '2024-12-01'),
('bench_012', 'role', 'sales_manager', 'team_revenue', 380000.0000, 280000.0000, 380000.0000, 480000.0000, 600000.0000, 'Internal Analytics', '2024-12-01');

COMMIT;

-- Territory performance analysis
CREATE OR REPLACE VIEW territory_analysis AS
WITH territory_metrics AS (
    SELECT
        u.territory,
        COUNT(DISTINCT u.user_id) AS sales_reps,
        COUNT(DISTINCT o.opportunity_id) AS total_opportunities,
        COUNT(DISTINCT CASE WHEN o.is_won = TRUE THEN o.opportunity_id END) AS won_opportunities,
        SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) AS total_revenue,
        COUNT(DISTINCT l.lead_id) AS total_leads,
        COUNT(DISTINCT CASE WHEN l.is_converted = TRUE THEN l.lead_id END) AS converted_leads,
        COUNT(DISTINCT c.company_id) AS unique_companies
    FROM salesgenie_ai.crm_data.sales_users u
    LEFT JOIN salesgenie_ai.crm_data.opportunities o ON u.user_id = o.sales_rep
    LEFT JOIN salesgenie_ai.crm_data.leads l ON u.user_id = l.assigned_to
    LEFT JOIN salesgenie_ai.crm_data.companies c ON (o.company_id = c.company_id OR l.company_id = c.company_id)
    WHERE u.territory IS NOT NULL
    GROUP BY u.territory
)
SELECT
    territory,
    sales_reps,
    total_opportunities,
    won_opportunities,
    total_revenue,
    total_leads,
    converted_leads,
    unique_companies,

    -- Performance metrics
    ROUND(total_revenue / NULLIF(sales_reps, 0), 2) AS revenue_per_rep,
    ROUND(won_opportunities * 100.0 / NULLIF(total_opportunities, 0), 2) AS win_rate_percent,
    ROUND(converted_leads * 100.0 / NULLIF(total_leads, 0), 2) AS lead_conversion_rate,
    ROUND(total_revenue / NULLIF(won_opportunities, 0), 2) AS avg_deal_size,

    -- Territory ranking
    RANK() OVER (ORDER BY total_revenue DESC) AS revenue_rank,
    RANK() OVER (ORDER BY won_opportunities * 100.0 / NULLIF(total_opportunities, 0) DESC) AS win_rate_rank

FROM territory_metrics
ORDER BY total_revenue DESC;

-- =====================================================
-- MACHINE LEARNING FUNCTIONS
-- =====================================================

-- Deal closure probability prediction
CREATE OR REPLACE FUNCTION predict_deal_closure_probability(
    stage VARCHAR,
    probability FLOAT,
    amount FLOAT,
    days_in_pipeline INT,
    company_revenue FLOAT,
    industry VARCHAR,
    sales_rep_win_rate FLOAT
)
RETURNS FLOAT
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
HANDLER = 'predict_closure'
AS
$$
def predict_closure(stage, probability, amount, days_in_pipeline, company_revenue, industry, sales_rep_win_rate):
    """
    Predict deal closure probability using ML-like algorithm
    Returns probability between 0 and 1
    """
    import math

    # Base probability from CRM
    base_prob = probability / 100.0 if probability else 0.5

    # Stage multipliers
    stage_multipliers = {
        'Discovery': 0.3,
        'Qualification': 0.5,
        'Proposal': 0.7,
        'Negotiation': 0.85,
        'Closed Won': 1.0,
        'Closed Lost': 0.0
    }
    stage_mult = stage_multipliers.get(stage, 0.5)

    # Amount factor (larger deals are harder to close)
    if amount > 100000:
        amount_factor = 0.9
    elif amount > 50000:
        amount_factor = 0.95
    else:
        amount_factor = 1.0

    # Time factor (deals get stale over time)
    if days_in_pipeline > 120:
        time_factor = 0.7
    elif days_in_pipeline > 60:
        time_factor = 0.85
    else:
        time_factor = 1.0

    # Company size factor
    if company_revenue and company_revenue > 10000000:
        company_factor = 1.1
    else:
        company_factor = 1.0

    # Industry factor
    high_conversion_industries = ['Technology', 'Financial Services']
    industry_factor = 1.1 if industry in high_conversion_industries else 1.0

    # Sales rep performance factor
    rep_factor = (sales_rep_win_rate / 100.0) if sales_rep_win_rate else 0.5
    rep_factor = max(0.5, min(1.5, rep_factor))  # Cap between 0.5 and 1.5

    # Calculate final probability
    final_prob = base_prob * stage_mult * amount_factor * time_factor * company_factor * industry_factor * rep_factor

    return max(0.0, min(1.0, final_prob))
$$;

-- Next best action recommendation
CREATE OR REPLACE FUNCTION recommend_next_action(
    record_type VARCHAR,  -- 'lead' or 'opportunity'
    stage VARCHAR,
    days_since_last_contact INT,
    sentiment_score FLOAT,
    amount FLOAT
)
RETURNS VARCHAR
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
HANDLER = 'get_recommendation'
AS
$$
def get_recommendation(record_type, stage, days_since_last_contact, sentiment_score, amount):
    """
    Recommend next best action based on record state
    """
    recommendations = []

    if record_type == 'lead':
        if days_since_last_contact > 14:
            recommendations.append("URGENT: Follow up immediately - lead is going cold")
        elif days_since_last_contact > 7:
            recommendations.append("Schedule follow-up call within 2 days")

        if sentiment_score and sentiment_score < -0.3:
            recommendations.append("Address concerns - negative sentiment detected")
        elif sentiment_score and sentiment_score > 0.3:
            recommendations.append("Strike while hot - positive sentiment, push for meeting")

        if stage == 'New':
            recommendations.append("Qualify lead - determine budget and timeline")
        elif stage == 'Contacted':
            recommendations.append("Send relevant case studies and schedule demo")
        elif stage == 'Qualified':
            recommendations.append("Convert to opportunity and create proposal")

    elif record_type == 'opportunity':
        if stage == 'Discovery':
            recommendations.append("Complete needs assessment and identify decision makers")
        elif stage == 'Qualification':
            recommendations.append("Present solution demo and gather requirements")
        elif stage == 'Proposal':
            if days_since_last_contact > 7:
                recommendations.append("Follow up on proposal - check for questions")
            else:
                recommendations.append("Schedule proposal review meeting")
        elif stage == 'Negotiation':
            recommendations.append("Address objections and finalize terms")

        if amount and amount > 100000:
            recommendations.append("Involve sales manager for high-value deal")

        if sentiment_score and sentiment_score < -0.2:
            recommendations.append("Risk mitigation needed - schedule stakeholder meeting")

    if not recommendations:
        recommendations.append("Continue regular follow-up and relationship building")

    return " | ".join(recommendations[:3])  # Return top 3 recommendations
$$;

-- =====================================================
-- ADVANCED ANALYTICS VIEWS
-- =====================================================

-- Sales velocity analysis
CREATE OR REPLACE VIEW sales_velocity_analysis AS
WITH deal_velocity AS (
    SELECT
        o.opportunity_id,
        o.opportunity_name,
        c.company_name,
        c.industry,
        o.amount,
        o.stage,
        o.sales_rep,
        o.created_date,
        o.actual_close_date,
        DATEDIFF('day', o.created_date, COALESCE(o.actual_close_date, CURRENT_DATE())) AS days_to_close,

        -- Calculate stage progression
        CASE o.stage
            WHEN 'Discovery' THEN 1
            WHEN 'Qualification' THEN 2
            WHEN 'Proposal' THEN 3
            WHEN 'Negotiation' THEN 4
            WHEN 'Closed Won' THEN 5
            WHEN 'Closed Lost' THEN 0
        END AS stage_number

    FROM salesgenie_ai.crm_data.opportunities o
    JOIN salesgenie_ai.crm_data.companies c ON o.company_id = c.company_id
    WHERE o.created_date >= DATEADD('month', -12, CURRENT_DATE())
)
SELECT
    industry,
    stage,
    COUNT(*) AS opportunity_count,
    AVG(days_to_close) AS avg_days_to_close,
    MEDIAN(days_to_close) AS median_days_to_close,
    AVG(amount) AS avg_deal_size,

    -- Velocity score (higher is better)
    ROUND(AVG(amount) / NULLIF(AVG(days_to_close), 0), 2) AS velocity_score,

    -- Conversion rate to next stage
    COUNT(CASE WHEN stage_number >= 2 THEN 1 END) * 100.0 / COUNT(*) AS discovery_conversion_rate,
    COUNT(CASE WHEN stage_number >= 3 THEN 1 END) * 100.0 / COUNT(*) AS qualification_conversion_rate,
    COUNT(CASE WHEN stage_number >= 4 THEN 1 END) * 100.0 / COUNT(*) AS proposal_conversion_rate,
    COUNT(CASE WHEN stage_number = 5 THEN 1 END) * 100.0 / COUNT(*) AS negotiation_conversion_rate

FROM deal_velocity
GROUP BY industry, stage
ORDER BY industry, stage;

-- Competitive analysis view
CREATE OR REPLACE VIEW competitive_analysis AS
WITH competitor_data AS (
    SELECT
        o.competitor,
        COUNT(*) AS total_deals,
        COUNT(CASE WHEN o.is_won = TRUE THEN 1 END) AS deals_won,
        COUNT(CASE WHEN o.is_lost = TRUE THEN 1 END) AS deals_lost,
        SUM(CASE WHEN o.is_won = TRUE THEN o.amount ELSE 0 END) AS revenue_won,
        SUM(CASE WHEN o.is_lost = TRUE THEN o.amount ELSE 0 END) AS revenue_lost,
        AVG(o.amount) AS avg_deal_size,
        AVG(DATEDIFF('day', o.created_date, COALESCE(o.actual_close_date, CURRENT_DATE()))) AS avg_sales_cycle
    FROM salesgenie_ai.crm_data.opportunities o
    WHERE o.competitor IS NOT NULL
    AND o.created_date >= DATEADD('month', -12, CURRENT_DATE())
    GROUP BY o.competitor
)
SELECT
    competitor,
    total_deals,
    deals_won,
    deals_lost,
    revenue_won,
    revenue_lost,
    avg_deal_size,
    avg_sales_cycle,

    -- Win rate against this competitor
    ROUND(deals_won * 100.0 / NULLIF(total_deals, 0), 2) AS win_rate_percent,

    -- Threat level
    CASE
        WHEN deals_won * 100.0 / NULLIF(total_deals, 0) < 30 THEN 'HIGH_THREAT'
        WHEN deals_won * 100.0 / NULLIF(total_deals, 0) < 50 THEN 'MEDIUM_THREAT'
        ELSE 'LOW_THREAT'
    END AS threat_level,

    -- Market share (by deal count)
    ROUND(total_deals * 100.0 / SUM(total_deals) OVER (), 2) AS market_share_percent

FROM competitor_data
ORDER BY total_deals DESC;

COMMIT;
